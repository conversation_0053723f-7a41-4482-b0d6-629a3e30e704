local ReplicatedStorage = game:GetService("ReplicatedStorage")
local player = game:GetService("Players")
local run = game:GetService("RunService")

local notifyManager = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local protocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local TrainController = require(ReplicatedStorage.Scripts.Client.Controller.TrainController)
local ItemInteractionSystem  = require(ReplicatedStorage.Scripts.ItemInteraction["ItemInteractionSystem "])
local ItemUI = require(ReplicatedStorage.Scripts.ItemUI.ItemUI)
local TillController = require(ReplicatedStorage.Scripts.Client.Controller.TillController)

local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local WeaponController = require(ReplicatedStorage.Scripts.Client.Controller.WeaponController)
local DeathBoxClient = require(ReplicatedStorage.Scripts.Client.Services.DeathBoxClient)
local AmmoInventoryService = require(ReplicatedStorage.Scripts.Client.Services.AmmoInventoryService)
local PlayerDownedClient = require(ReplicatedStorage.Scripts.Client.Services.PlayerDownedClient)
local BandageController = require(ReplicatedStorage.Scripts.Client.Controller.BandageController)
local NewPlayerBoxClient = require(ReplicatedStorage.Scripts.Client.Services.NewPlayerBoxClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)
local DownedPlayerHighlightService = require(ReplicatedStorage.Scripts.Client.Services.DownedPlayerHighlightService)


local EquipManager = require(ReplicatedStorage.Scripts.Equip.EquipManager)
local WeaponEquip =  require(ReplicatedStorage.Scripts.Equip.WeaponEquip)
local ProfessionSwitch	= require(ReplicatedStorage.Scripts.Client.Controller.ProfessionSwith)
local SpecialCurrencyControl = require(ReplicatedStorage.Scripts.Client.Controller.SpecialCurrencyController)
local ClientEnter = {}

function ClientEnter.Init()
	print("客户端初始化")
	-- 再初始化其他系统
	protocolManager.InitClientProtocol()
	ProfessionSwitch.InitClient()
	-- 初始化武器客户端
	if type(WeaponClient) == "table" then
		WeaponClient:Initialize()
		print("WeaponClient 初始化成功")
	else
		warn("WeaponClient 获取失败")
	end
	TrainController.init()
	ItemInteractionSystem.initClient()
	ItemUI.InitClient()
	TillController.InitClient()
	WeaponEquip.InitClient()
	-- 初始化武器控制器
	WeaponController.Init()
	EquipManager.InitClient()
	-- 初始化死亡盒子客户端
	DeathBoxClient:Initialize()	


	-- 初始化弹药库系统
	if type(AmmoInventoryService) == "table" then
		AmmoInventoryService:Initialize()
		print("AmmoInventoryService 初始化成功")
	else
		warn("AmmoInventoryService 获取失败")
	end
	-- 初始化玩家倒地系统
	if type(PlayerDownedClient) == "table" then
		PlayerDownedClient:Initialize()
		print("PlayerDownedClient 初始化成功")
	else
		warn("PlayerDownedClient 获取失败")
	end
	-- 初始化绷带控制器
	if type(BandageController) == "table" then
		BandageController:Initialize()
		print("BandageController 初始化成功")
	else
		warn("BandageController 获取失败")
	end
	-- 初始化摄像机控制服务
	if type(CameraControlService) == "table" then
		CameraControlService:Initialize()
		print("CameraControlService 初始化成功")
	else
		warn("CameraControlService 获取失败")
	end

	-- 初始化濒死玩家高亮显示服务
	if type(DownedPlayerHighlightService) == "table" then
		DownedPlayerHighlightService:Initialize()
		print("DownedPlayerHighlightService 初始化成功")
	else
		warn("DownedPlayerHighlightService 获取失败")
	end

	-- 初始化新手盒子客户端
	if type(NewPlayerBoxClient) == "table" then
		NewPlayerBoxClient:Initialize()
		print("NewPlayerBoxClient 初始化成功")
	else
		warn("NewPlayerBoxClient 获取失败")
	end



	SpecialCurrencyControl.InitClient()
	print("客户端初始化完成")
end

return ClientEnter