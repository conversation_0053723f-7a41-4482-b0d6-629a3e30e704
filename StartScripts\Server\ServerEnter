local ReplicatedStorage = game:GetService("ReplicatedStorage")
local player = game:GetService("Players")
local notifyManager = require(ReplicatedStorage.Scripts.Share.Manager.NotifyManager)
local protocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)

local TouchManager=require(ReplicatedStorage.Scripts.Server.Manager.TouchManager)
local RoomManager=require(ReplicatedStorage.Scripts.Server.Manager.RoomManager)
local SpecialCurrency = require(ReplicatedStorage.Scripts.Server.Manager.SpecialCurrencyManager)
local ProfessionManager = require(ReplicatedStorage.Scripts.Server.Manager.ProfessionManager)
local ServerEnter = {}


function ServerEnter.Init()	
	print("服务端初始化")
	
	notifyManager.GenerateRemote()
	protocolManager.GenerateRemote()
	SpecialCurrency.InitServer()
	ProfessionManager.InitServer()
	
	
	print("服务端初始化完成")
	
end

return ServerEnter
