local ReplicatedStorage = game:GetService("ReplicatedStorage")
local player = game:GetService("Players")
local notifyManager = require(ReplicatedStorage.Scripts.Share.Manager.NotifyManager)
local protocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)

local TouchManager=require(ReplicatedStorage.Scripts.Server.Manager.TouchManager)
local RoomManager=require(ReplicatedStorage.Scripts.Server.Manager.RoomManager)
local SpecialCurrency = require(ReplicatedStorage.Scripts.Server.Manager.SpecialCurrencyManager)
local ProfessionManager = require(ReplicatedStorage.Scripts.Server.Manager.ProfessionManager)
local SimpleTest = require(ReplicatedStorage.Scripts.Server.Utils.SimpleTest)
local ServerEnter = {}


function ServerEnter.Init()	
	print("服务端初始化")
	
	notifyManager.GenerateRemote()
	protocolManager.GenerateRemote()
	SpecialCurrency.InitServer()
	ProfessionManager.InitServer()

	-- 设置简单测试命令
	SimpleTest.setupCommands()

	print("服务端初始化完成")
	print("可用测试命令: /help_simple")
	
end

return ServerEnter
