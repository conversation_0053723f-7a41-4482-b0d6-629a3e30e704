--!strict
-- 物品管理器
-- 用于处理玩家的物品数据，包括给予物品、管理背包等

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TeleportDataReceiver = require(ReplicatedStorage.Scripts.Server.Manager.TeleportDataReceiver)

local ItemManager = {}

-- 物品类型定义
ItemManager.ItemTypes = {
    CONSUMABLE = 6,      -- 消耗品
    MATERIAL = 7,        -- 材料
    WEAPON = 10,         -- 武器
    CONSUMABLE_BAG = 11  -- 消耗品（放背包）
}

-- 存储玩家的物品数据
ItemManager.playerItems = {}

-- 根据物品类型确定放置位置
-- @param itemType: number - 物品类型
-- @return string - 放置位置 ("backpack" 或 "toolbar")
local function getItemPlacement(itemType)
    if itemType == ItemManager.ItemTypes.WEAPON then
        return "toolbar"  -- 武器放工具栏
    else
        return "backpack" -- 其他物品放背包
    end
end

-- 给玩家添加单个物品
-- @param player: Player - 玩家对象
-- @param itemData: table - 物品数据 {id, quantity, itemType}
-- @return boolean - 是否成功添加
local function givePlayerItem(player, itemData)
    if not player or not itemData then
        warn("ItemManager: 无效的玩家或物品数据")
        return false
    end
    
    local itemId = itemData.id
    local quantity = itemData.quantity
    local itemType = itemData.itemType
    
    if not itemId or not quantity or not itemType then
        warn("ItemManager: 物品数据不完整", itemData)
        return false
    end
    
    local placement = getItemPlacement(itemType)
    
    print(string.format("ItemManager: 给玩家 %s 添加物品 ID:%d, 数量:%d, 类型:%d, 位置:%s", 
        player.Name, itemId, quantity, itemType, placement))
    
    -- 这里应该调用实际的物品系统API
    -- 由于我不知道具体的物品系统实现，这里只是示例代码
    -- 实际使用时需要替换为真实的物品给予逻辑
    
    -- 示例：可能的物品系统调用
    -- local ItemSystem = require(ReplicatedStorage.Scripts.Server.Manager.ItemSystem)
    -- return ItemSystem.giveItem(player, itemId, quantity, placement)
    
    -- 临时存储到我们的数据结构中
    local userId = tostring(player.UserId)
    if not ItemManager.playerItems[userId] then
        ItemManager.playerItems[userId] = {
            backpack = {},
            toolbar = {}
        }
    end
    
    table.insert(ItemManager.playerItems[userId][placement], {
        id = itemId,
        quantity = quantity,
        itemType = itemType
    })
    
    return true
end

-- 给玩家添加所有传送过来的物品
-- @param player: Player - 玩家对象
-- @return boolean - 是否成功添加所有物品
function ItemManager.givePlayerTeleportItems(player)
    local items = TeleportDataReceiver.getPlayerItems(player)
    
    if not items or #items == 0 then
        print("ItemManager: 玩家 " .. player.Name .. " 没有需要添加的物品")
        return true
    end
    
    print("ItemManager: 开始给玩家 " .. player.Name .. " 添加 " .. #items .. " 个物品")
    
    local successCount = 0
    for i, item in ipairs(items) do
        if givePlayerItem(player, item) then
            successCount = successCount + 1
        else
            warn("ItemManager: 给玩家 " .. player.Name .. " 添加物品失败", item)
        end
    end
    
    print("ItemManager: 成功给玩家 " .. player.Name .. " 添加 " .. successCount .. "/" .. #items .. " 个物品")
    return successCount == #items
end

-- 获取玩家的物品列表
-- @param player: Player - 玩家对象
-- @param placement: string - 位置 ("backpack", "toolbar", 或 nil 表示全部)
-- @return table - 物品列表
function ItemManager.getPlayerItems(player, placement)
    local userId = tostring(player.UserId)
    local playerItems = ItemManager.playerItems[userId]
    
    if not playerItems then
        return {}
    end
    
    if placement then
        return playerItems[placement] or {}
    else
        -- 返回所有物品
        local allItems = {}
        for _, items in pairs(playerItems) do
            for _, item in ipairs(items) do
                table.insert(allItems, item)
            end
        end
        return allItems
    end
end

-- 清理玩家物品数据
-- @param player: Player - 玩家对象
function ItemManager.cleanupPlayerItems(player)
    local userId = tostring(player.UserId)
    ItemManager.playerItems[userId] = nil
    print("ItemManager: 清理玩家 " .. player.Name .. " 的物品数据")
end

-- 验证物品数据格式
-- @param items: table - 物品数组
-- @return boolean - 是否有效
function ItemManager.validateItems(items)
    if not items or type(items) ~= "table" then
        return false
    end
    
    for _, item in ipairs(items) do
        if type(item) ~= "table" or 
           not item.id or not item.quantity or not item.itemType or
           type(item.id) ~= "number" or 
           type(item.quantity) ~= "number" or 
           type(item.itemType) ~= "number" then
            return false
        end
    end
    
    return true
end

-- 初始化物品管理器
function ItemManager.init()
    -- 监听玩家加入事件
    Players.PlayerAdded:Connect(function(player)
        -- 等待一小段时间确保传送数据已处理
        task.wait(1)
        
        -- 给玩家添加传送过来的物品
        ItemManager.givePlayerTeleportItems(player)
    end)
    
    -- 监听玩家离开事件
    Players.PlayerRemoving:Connect(function(player)
        ItemManager.cleanupPlayerItems(player)
    end)
    
    print("ItemManager: 初始化完成")
end

-- 调试函数：打印玩家物品
-- @param player: Player - 玩家对象
function ItemManager.debugPrintPlayerItems(player)
    local userId = tostring(player.UserId)
    local playerItems = ItemManager.playerItems[userId]
    
    print("=== 玩家 " .. player.Name .. " 的物品 ===")
    
    if not playerItems then
        print("  无物品数据")
        return
    end
    
    print("  背包物品:")
    for i, item in ipairs(playerItems.backpack or {}) do
        print(string.format("    [%d] ID:%d, 数量:%d, 类型:%d", 
            i, item.id, item.quantity, item.itemType))
    end
    
    print("  工具栏物品:")
    for i, item in ipairs(playerItems.toolbar or {}) do
        print(string.format("    [%d] ID:%d, 数量:%d, 类型:%d", 
            i, item.id, item.quantity, item.itemType))
    end
    
    print("================================")
end

-- 调试函数：打印所有玩家物品
function ItemManager.debugPrintAllItems()
    print("=== ItemManager 所有玩家物品 ===")
    for userId, _ in pairs(ItemManager.playerItems) do
        local player = Players:GetPlayerByUserId(tonumber(userId))
        if player then
            ItemManager.debugPrintPlayerItems(player)
        end
    end
    print("===============================")
end

return ItemManager
