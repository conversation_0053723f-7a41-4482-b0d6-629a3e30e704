local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players			= game:GetService("Players")
local StartGui			= game:GetService("StarterGui")
local UserInputService 	= game:GetService("UserInputService") -- 添加输入服务
local ProtocolManager 	= require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local NotifyManager 	= require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local ContextActionService  = game:GetService("ContextActionService")
local cam                   = workspace.CurrentCamera
-- 等待 LocalPlayer 可用
local player 			= Players.LocalPlayer or Players.PlayerAdded:Wait()
local playerGui 		= player:WaitForChild("PlayerGui")
local ModelID			= require(ReplicatedStorage.Scripts.ItemInteraction.ModelID)
local CreatObj 			= require(ReplicatedStorage.Scripts.ItemInteraction.CreatObjAndGUId)
local ObjectTracker 	= require(ReplicatedStorage.Scripts.ItemInteraction.ObjectTracker) 
local State				= require(ReplicatedStorage.Scripts.State.StateManager)
local PlayerDownedClient = require(ReplicatedStorage.Scripts.Client.Services.PlayerDownedClient)
local AmmoInventoryService = require(ReplicatedStorage.Scripts.Client.Services.AmmoInventoryService)

local prevCamType

local ItemUI = {}

local Item_UI = {}
local Equip_UI = {}
local StateText = 
	{
		HealthText = nil,
		MoveSpeedText = nil,
		AttackText = nil,
	}
local player = nil
local BagScreenGui = nil
local ModelFloder = nil -- Item文件夹
local isGuiOpen = false	
local isFirstPerson = false
local count = 0 --当前物品数量
local validItemCount = 0 --总数量
local DEFAULT_IMAGE = "rbxassetid://0"  -- 默认空白图片
local countText
local itemBag
local stateBag
local startUI
local ViewportFrame 
local function InitGui()
	Item_UI = {}
	Equip_UI = {}
	count = 0 
	validItemCount = 0
	ModelFloder = ReplicatedStorage:FindFirstChild("Model"):FindFirstChild("Item")
	-- 查找ScreenGui并确保其存在
	BagScreenGui = playerGui:WaitForChild("ScreenGui",5)
	if not BagScreenGui then
		print("错误: 未找到Bag ScreenGui")
		return
	end

	itemBag = BagScreenGui:FindFirstChild("Bag")
	local background = itemBag:FindFirstChild("Background")	
	local stateButton = itemBag:FindFirstChild("StateButton")
	countText	= itemBag:FindFirstChild("CountText")

	stateBag = BagScreenGui:FindFirstChild("State")
	local bagButton = stateBag:FindFirstChild("BagButton")
	local stateText = stateBag:FindFirstChild("StateText")
	local equipText = stateBag:FindFirstChild("EquipText")


	-- 查找已创建的弹药库按钮
	local ammoButton = itemBag:FindFirstChild("AmmoButton")
	if not ammoButton then
		warn("警告: 未找到弹药库按钮")
	else
		-- 弹药库按钮点击事件
		ammoButton.MouseButton1Click:Connect(function()
			AmmoInventoryService:ToggleAmmoInventoryUI()
		end)
	end


	-- 查找已创建的弹药库按钮
	local ammoButton = itemBag:FindFirstChild("AmmoButton")
	if not ammoButton then
		warn("警告: 未找到弹药库按钮")
	else
		-- 弹药库按钮点击事件
		ammoButton.MouseButton1Click:Connect(function()

		end)
	end

	if background then
		-- 打印子元素数量
		local children = background:GetChildren()

		for i, itemUI in ipairs(children) do
			local item = itemUI
			if item.Name == "Item" then
				--print("  找到ItemImage，类型:", item.ClassName)

				if item:IsA("ImageLabel") then

					local itemId	= item:FindFirstChild("ItemId")
					local itemImage = item:FindFirstChild("ItemImage")
					local itemName = item:FindFirstChild("ItemName")
					local dropButton = item:FindFirstChild("DropButton")
					local itemType	= item:FindFirstChild("Type")
					if itemName and dropButton and itemImage and itemId then
						validItemCount = validItemCount + 1

						-- 确保 ItemID 初始值为 0
						if itemId.Value then
							itemId.Value = 0
						end
						if itemType.Value then
							itemType.Value = 0
						end
						itemName.Text = ""
						itemName.BackgroundTransparency = 1

						Item_UI[validItemCount] = {item,itemId, itemImage, itemName,dropButton,itemType}
					end
				else
					print("  错误: ItemImage不是ImageLabel类型")
				end
				item.Visible = false
			else
				print("  未找到ItemImage")
			end
		end
		if countText then
			countText.Text = count .. "  /  " .. validItemCount
		end
		--print("共初始化", validItemCount, "个有效ItemUI组件")
	else
		warn("错误: Background对象不存在")
	end
	if stateButton and bagButton then
		print("绑定UI切换按键")
		stateButton.MouseButton1Click:Connect(function()
			itemBag.Visible = false
			stateBag.Visible = true
		end)
		bagButton.MouseButton1Click:Connect(function()
			itemBag.Visible = true
			stateBag.Visible = false
		end)
	end
	if stateText then
		local healthFrame = stateText:FindFirstChild("Health")
		if healthFrame then
			local healthText = healthFrame:FindFirstChild("TextLabel")
			if healthText then
				StateText.HealthText = healthText
			else
				warn("未找到生命值显示文本")
			end
		else
			warn("未找到Health组件")
		end
		local moveSpeedFrame = stateText:FindFirstChild("MoveSpeed")
		if moveSpeedFrame then
			local moveSpeedText = moveSpeedFrame:FindFirstChild("TextLabel")
			if moveSpeedText then
				StateText.MoveSpeedText = moveSpeedText
			else
				warn("未找到速度显示文本")
			end
		else
			warn("未找到MoveSpeed组件")
		end
		local AtackFrame = stateText:FindFirstChild("Attack")
		if AtackFrame then
			local attackText = AtackFrame:FindFirstChild("TextLabel")
			if attackText then
				StateText.AttackText = attackText
			else
				warn("未找到攻击值显示文本")
			end
		else
			warn("未找到Attack组件")
		end
	end

	if equipText then
		for _ , equipUI in ipairs(equipText:GetChildren()) do
			if equipUI:IsA("ImageLabel") then
				table.insert(Equip_UI,equipUI)
			end
		end
	end
	-- 设置初始状态
	BagScreenGui.Enabled = isGuiOpen
	print("背包界面初始状态:", isGuiOpen and "打开" or "关闭")
end
function ItemUI.updateEquipUI(equip,equipPart,icon)
	for _,equip in ipairs(Equip_UI) do
		if equipPart == equip.Name then
			equip.Image = icon
		end
	end
end
-- 更新UI
function ItemUI.updateItemUI(id,image,name,itemType)
	-- 参数验证
	if typeof(image) ~= "string" or typeof(name) ~= "string" then
		print("错误: 参数类型不正确")
		return false
	end

	-- 修复：验证itemType参数，如果为nil则设置为0
	if itemType == nil then
		print("警告: itemType为nil，设置为默认值0")
		itemType = 0
	elseif typeof(itemType) ~= "number" then
		print("警告: itemType不是数字类型，设置为默认值0")
		itemType = 0
	end

	for i, item in ipairs(Item_UI) do
		local itemParent = item[1]
		local itemId = item[2]
		local itemImage = item[3]
		local itemName = item[4]
		local dropButton = item[5]
		local currentType = item[6]
		-- 检查UI元素有效性
		if not (itemImage and itemName and dropButton and currentType) then
			continue
		end

		if itemId and itemId.Value == 0 then
			itemParent.Visible = true
			count += 1
			-- 记录更新前状态
			print("槽位", i, "更新前 - Image:", itemImage.Image, "Text:", itemName.Text)

			-- 更新属性
			itemImage.Image = image
			itemName.Text = name
			itemName.BackgroundTransparency = 0.8
			itemId.Value = id
			currentType.Value = itemType
			-- 延迟验证更新
			print("槽位", i, "更新后 - Image:", itemImage.Image, "Text:", itemName.Text,"ID:",itemId.Value)

			-- 绑定事件
			dropButton.MouseButton1Click:Connect(function()
				if itemImage and itemName and itemId and itemId.Value ~= 0 then
					count -= 1
					dropItem(itemId.Value)
					itemImage.Image = DEFAULT_IMAGE 
					itemName.Text = ""
					itemName.BackgroundTransparency = 1
					itemId.Value = 0
					itemParent.Visible = false
					countText.Text = count .. "  /  " .. validItemCount
					currentType.Value = 0
				end

			end)
			countText.Text = count .. "  /  " .. validItemCount
			return true
		end
	end
	return false
end

function ItemUI.UpdateStateUI(data)
	StateText.HealthText.Text = data.Health
	StateText.MoveSpeedText.Text = data.MoveSpeed
	StateText.AttackText.Text = data.Attack
end
-- 切换背包界面显示状态
local function setBagEnable()

	-- 新增：检查是否处于濒死状态，如果是则不允许打开背包
	if PlayerDownedClient.IsDowned then
		print ("濒死状态下无法打开背包")
		return
	end

	if not BagScreenGui then
		warn("错误: BagScreenGui未初始化")
		return
	end

	local prevState = isGuiOpen
	isGuiOpen = not isGuiOpen
	print(("切换背包状态: %s → %s"):format(
		prevState and "打开" or "关闭",
		isGuiOpen and "打开" or "关闭"
		))

	local success, err = pcall(function()
		BagScreenGui.Enabled = isGuiOpen
		if isGuiOpen then
			--打开UI首先显示背包界面
			itemBag.Visible = true
			stateBag.Visible = false
			-- 解锁并显示鼠标
			UserInputService.MouseBehavior    = Enum.MouseBehavior.Default
			UserInputService.MouseIconEnabled = true

			-- 切换摄像机模式
			prevCamType = cam.CameraType
			cam.CameraType = Enum.CameraType.Scriptable

		else
			-- 背包关闭时，同时关闭弹药库UI
			if AmmoInventoryService and AmmoInventoryService.IsUIOpen then
				AmmoInventoryService:ToggleAmmoInventoryUI()
				print("背包关闭，同时关闭弹药库UI")
			end
			-- 恢复摄像机模式
			if prevCamType then
				cam.CameraType = prevCamType
				prevCamType = nil
			end

		end

		print(("当前鼠标行为: %s"):format(UserInputService.MouseBehavior.Name))
		print(("当前摄像机模式: %s"):format(cam.CameraType.Name))
	end)

	if not success then
		warn(("设置背包界面状态失败: %s"):format(err))
		isGuiOpen = prevState
	end
end

function dropItem(id)
	if not CreatObj or not CreatObj.createGrabbableObject then
		print("错误: CreatObj模块或createGrabbableObject方法不存在")
		return
	end
	local character = player.Character
	if character and character:FindFirstChild("HumanoidRootPart") then
		local HumanoidRootPart = character.HumanoidRootPart
		local forwardVector = HumanoidRootPart.CFrame.LookVector
		local position = HumanoidRootPart.Position + forwardVector * 2
		ProtocolManager.SendMessage("ItemDropEvent",{Id = id,position = position})
	end
end

function ItemUI.InitClient()
	player = Players.LocalPlayer
	InitGui()
	ItemUI.UpdateStateUI(State.State)
	NotifyManager.RegisterClientEvent("UpdateState",function(data)		
		ItemUI.UpdateStateUI(data.State)
		State.UpdateState(data.State) -- 客户端记录属性
	end)
	NotifyManager.RegisterClientEvent("UpdateEquipUI",function(data)
		ItemUI.updateEquipUI(data.equipPartStr,data.iconStr)
	end)

	-- 注册ItemUI物品恢复事件
	NotifyManager.RegisterClientEvent("RestoreItemUIData", function(data)
		if data.items then
			ItemUI.RestoreItems(data.items)
		end
	end)

	-- 注册濒死状态事件，清空背包并发送数据给服务端
	NotifyManager.RegisterClientEvent("PlayerDowned", function(data)
		-- 收集当前背包物品数据
		local itemsData = ItemUI.GetAllItems()

		-- 清空背包显示
		ItemUI.ClearAllItems()

		-- 发送物品数据给服务端
		if #itemsData > 0 then
			ProtocolManager.SendMessage("SendItemUIDataEvent", {
				items = itemsData
			})
			print("已发送", #itemsData, "个ItemUI物品数据给服务端")
		end
	end)
	-- 使用B键切换背包
	UserInputService.InputBegan:Connect(function(input, gameProcessedEvent)
		if gameProcessedEvent or PlayerDownedClient.IsDowned then return end
		if input.KeyCode == Enum.KeyCode.B then
			setBagEnable()
		end
	end)

	print("ItemUI客户端初始化完成")
end

-- 获取所有背包物品数据
function ItemUI.GetAllItems()
	local items = {}

	for i, itemUI in ipairs(Item_UI) do
		local itemParent = itemUI[1]
		local itemId = itemUI[2]
		local itemImage = itemUI[3]
		local itemName = itemUI[4]
		local itemType = itemUI[6] -- 修复：正确的索引，itemType在第6位

		-- 检查槽位是否有物品（ID不为0）
		if itemId and itemId.Value ~= 0 then
			local itemTypeValue = nil
			if itemType and itemType.Value then
				itemTypeValue = itemType.Value
			end

			table.insert(items, {
				id = itemId.Value,
				image = itemImage.Image,
				name = itemName.Text,
				itemType = itemTypeValue, -- 修复：使用正确的变量
				slotIndex = i
			})
		end
	end

	print("ItemUI.GetAllItems: 收集到", #items, "个物品")
	for i, itemData in ipairs(items) do
		print("  物品", i, "- ID:", itemData.id, "名称:", itemData.name, "类型:", itemData.itemType)
	end
	return items
end

-- 批量恢复物品到背包
function ItemUI.RestoreItems(itemsData)
	if not itemsData or #itemsData == 0 then
		print("ItemUI.RestoreItems: 没有物品需要恢复")
		return
	end

	print("ItemUI.RestoreItems: 开始恢复", #itemsData, "个物品")

	for _, itemData in ipairs(itemsData) do
		local success = ItemUI.updateItemUI(
			itemData.id,
			itemData.image,
			itemData.name,
			itemData.itemType -- 修复：使用正确的字段名
		)

		if success then
			print("成功恢复物品:", itemData.name, "ID:", itemData.id)
		else
			print("恢复物品失败:", itemData.name, "ID:", itemData.id, "背包可能已满")
		end
	end
end

-- 清空所有背包物品（用于濒死时）
function ItemUI.ClearAllItems()
	local clearedItems = {}

	for i, itemUI in ipairs(Item_UI) do
		local itemParent = itemUI[1]
		local itemId = itemUI[2]
		local itemImage = itemUI[3]
		local itemName = itemUI[4]
		local itemType = itemUI[6] -- 修复：添加物品类型读取

		-- 如果槽位有物品，记录并清空
		if itemId and itemId.Value ~= 0 then
			local itemTypeValue = nil
			if itemType and itemType.Value then
				itemTypeValue = itemType.Value
			end

			table.insert(clearedItems, {
				id = itemId.Value,
				image = itemImage.Image,
				name = itemName.Text,
				itemType = itemTypeValue, -- 修复：保存物品类型
				slotIndex = i
			})

			-- 清空槽位
			itemImage.Image = DEFAULT_IMAGE
			itemName.Text = ""
			itemName.BackgroundTransparency = 1
			itemId.Value = 0
			if itemType then
				itemType.Value = 0 -- 修复：同时清空物品类型
			end
			itemParent.Visible = false
			count = count - 1
		end
	end

	-- 更新计数显示
	if countText then
		countText.Text = count .. "  /  " .. validItemCount
	end

	print("ItemUI.ClearAllItems: 清空了", #clearedItems, "个物品")
	return clearedItems
end

return ItemUI