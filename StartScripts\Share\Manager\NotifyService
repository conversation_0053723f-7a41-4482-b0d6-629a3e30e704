--服务端发送给客户端 
local replicatedStorage=game:GetService("ReplicatedStorage")

local NotifyService = {}


NotifyService.remoteFolder=replicatedStorage.Remotes

NotifyService.EventData={


	LoadUI="LoadUI",
	LoadLeftUI="LoadLeftUI",

	UpdataUI="UpdataUI",
	RemoveRoom="RemoveRoom",

	StartTime="StartTime",
	StopTime="StopTime",

	IsLoad="IsLoad",
	UpdateCurrencyUI = "UpdateCurrencyUI",
	HasProfession = "HasProfession",
	RecordProfession = "RecordProfession",
	TeleportError = "TeleportError",
}


--服务端初始化生成remoteevent
function NotifyService.GenerateRemote()
	--添加初始的Remote
	local remotes=replicatedStorage.Remotes:GetChildren()
	for k, v in pairs(remotes) do
		if not NotifyService[v.Name] then
			NotifyService[v.Name]=v
		end
	end
	
	for k,v in pairs(NotifyService.EventData) do
		if NotifyService[k] then
			--print("已经存在名为",k,"的RemoteEvent,请定义其他的RemoteEvent.")
		else
			local remoteEvent=Instance.new("RemoteEvent")
			remoteEvent.Name=k
			remoteEvent.Parent=NotifyService.remoteFolder
			NotifyService[k]=remoteEvent
		end
	end
end


function NotifyService.FireClient(remoteEventName,player,message)
	local remoteEvent=NotifyService[remoteEventName]
	if	remoteEvent then
		remoteEvent:FireClient(player,message)
	end
end


function NotifyService.FireAllClient(remoteEventName,message)
	local remoteEvent=NotifyService[remoteEventName]
	if	remoteEvent then
		remoteEvent:FireAllClients(message)
	else
		print("RemoteEventName is not exist:",remoteEventName)
	end
end


--客户端注册事件
function NotifyService.RegisterClientEvent(remoteEventName,callback)
	local remoteEvent=NotifyService.remoteFolder:WaitForChild(remoteEventName)
	if remoteEvent then
		remoteEvent.OnClientEvent:Connect(callback)
	end
end

return NotifyService
