--!strict
-- 新手装备盒子配置文件
-- 定义新玩家生成时获得的初始装备和物品

export type BoxItem = {
	id: number,
	quantity: number,
	itemType: number -- ItemType from ItemConfig
}

export type BoxAppearance = {
	size: Vector3,
	color: Color3,
	material: Enum.Material,
	glowEffect: boolean,
	glowColor: Color3
}

export type NewPlayerBoxConfig = {
	items: {BoxItem}, -- 统一的物品配置
	boxAppearance: BoxAppearance,
	spawnSettings: {
		enabled: boolean,
		spawnRadius: number,
		spawnHeight: number,
		maxAttempts: number
	}
}

local NewPlayerBoxConfig = {
	-- 新手物品配置（包含武器和其他物品）
	items = {
		-- 近战武器 (ItemType = 9)
		{
			id = 10031, -- 木棒
			quantity = 1,
			itemType = 9
		},
		-- 远程武器 (ItemType = 10)
		{
			id = 10052, -- 冲锋枪 MP5K
			quantity = 1,
			itemType = 10
		},
		-- 消耗品 (ItemType = 7)
		{
			id = 10026, -- 绷带
			quantity = 3,
			itemType = 7
		},
		-- 装备 (ItemType = 11)
		{
			id = 10039, -- 铁头盔 Black_Head
			quantity = 1,
			itemType = 11
		},
		-- 子弹 (ItemType = 6)
		{
			id = 10029, -- 手枪子弹
			quantity = 50,
			itemType = 6
		}
	},

	-- 盒子外观配置
	boxAppearance = {
		size = Vector3.new(4, 2.5, 4),
		color = Color3.new(0.2, 0.8, 0.2), -- 绿色
		material = Enum.Material.ForceField,
		glowEffect = true,
		glowColor = Color3.new(0, 1, 0) -- 亮绿色发光
	},

	-- 生成设置
	spawnSettings = {
		enabled = true, -- 是否启用新手盒子系统
		spawnRadius = 8, -- 在玩家周围8米范围内生成
		spawnHeight = 1, -- 盒子底部距离地面的高度
		maxAttempts = 10 -- 寻找合适生成位置的最大尝试次数
	}
}

-- 获取所有物品配置
function NewPlayerBoxConfig.GetItems()
	return NewPlayerBoxConfig.items
end

-- 获取武器配置（ItemType 9和10）
function NewPlayerBoxConfig.GetWeapons()
	local weapons = {}
	for _, item in ipairs(NewPlayerBoxConfig.items) do
		if item.itemType == 9 or item.itemType == 10 then
			table.insert(weapons, item)
		end
	end
	return weapons
end

-- 获取ItemUI物品配置（非武器）
function NewPlayerBoxConfig.GetItemUIItems()
	local itemUIItems = {}
	for _, item in ipairs(NewPlayerBoxConfig.items) do
		if item.itemType ~= 9 and item.itemType ~= 10 then
			table.insert(itemUIItems, item)
		end
	end
	return itemUIItems
end

-- 获取盒子外观配置
function NewPlayerBoxConfig.GetBoxAppearance()
	return NewPlayerBoxConfig.boxAppearance
end

-- 获取生成设置
function NewPlayerBoxConfig.GetSpawnSettings()
	return NewPlayerBoxConfig.spawnSettings
end

-- 检查系统是否启用
function NewPlayerBoxConfig.IsEnabled()
	return NewPlayerBoxConfig.spawnSettings.enabled
end

-- 根据ID查找物品配置
function NewPlayerBoxConfig.FindItemById(itemId): BoxItem?
	for _, item in ipairs(NewPlayerBoxConfig.items) do
		if item.id == itemId then
			return item
		end
	end
	return nil
end

-- 获取所有配置的物品ID列表
function NewPlayerBoxConfig.GetItemIds()
	local ids = {}
	for _, item in ipairs(NewPlayerBoxConfig.items) do
		table.insert(ids, item.id)
	end
	return ids
end

-- 根据ItemType分类获取物品
function NewPlayerBoxConfig.GetItemsByType(itemType)
	local items = {}
	for _, item in ipairs(NewPlayerBoxConfig.items) do
		if item.itemType == itemType then
			table.insert(items, item)
		end
	end
	return items
end

-- 验证配置的有效性
function NewPlayerBoxConfig.ValidateConfig()
	local errors = {}

	-- 检查物品配置
	if #NewPlayerBoxConfig.items == 0 then
		table.insert(errors, "至少需要配置一个物品")
	end

	for i, item in ipairs(NewPlayerBoxConfig.items) do
		if not item.id or item.id <= 0 then
			table.insert(errors, "物品 " .. i .. " 的ID无效")
		end
		if not item.quantity or item.quantity <= 0 then
			table.insert(errors, "物品 " .. i .. " 的数量无效")
		end
		if not item.itemType or item.itemType < 0 then
			table.insert(errors, "物品 " .. i .. " 的ItemType无效")
		end
	end

	-- 检查生成设置
	local settings = NewPlayerBoxConfig.spawnSettings
	if settings.spawnRadius <= 0 then
		table.insert(errors, "生成半径必须大于0")
	end
	if settings.maxAttempts <= 0 then
		table.insert(errors, "最大尝试次数必须大于0")
	end

	return #errors == 0, errors
end

return NewPlayerBoxConfig
