local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local ServerStorage = game:GetService("ServerStorage")
local RemoteWeaponConfig = require(ReplicatedStorage.Scripts.Config.RemoteWeaponConfig)
local ProtocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local AmmoInventoryServer = {}

-- 玩家弹药库存数据 {[playerId] = {[ammoId] = {id, name, amount, modelName, weaponId}}}
AmmoInventoryServer.PlayerAmmoInventories = {}

-- 初始化服务
function AmmoInventoryServer:Initialize()
	print("初始化服务器弹药库系统")

	-- 监听玩家加入
	Players.PlayerAdded:Connect(function(player)
		self:InitializePlayerAmmoInventory(player)
	end)

	-- 监听玩家离开
	Players.PlayerRemoving:Connect(function(player)
		self:SavePlayerAmmoInventory(player)
		self.PlayerAmmoInventories[player.UserId] = nil
	end)


	print("服务器弹药库系统初始化完成")
end

-- 初始化玩家弹药库
function AmmoInventoryServer:InitializePlayerAmmoInventory(player)
	if not player then return end

	local userId = player.UserId

	-- 检查是否已初始化
	if self.PlayerAmmoInventories[userId] then return end

	-- 创建新的弹药库
	local ammoInventory = {}

	-- 从RemoteWeaponConfig中提取所有弹药类型
	-- 现在使用BallisticId（即ItemID）作为弹药ID
	for _, config in ipairs(RemoteWeaponConfig) do
		if config.BallisticId and not ammoInventory[config.BallisticId] then
			-- 获取子弹物品信息
			local bulletName = self:GetBulletNameById(config.BallisticId)

			ammoInventory[config.BallisticId] = {
				id = config.BallisticId,
				name = bulletName or config.BallisticModel or "未知弹药",
				amount = 0, -- 初始数量为0
				modelName = config.BallisticModel,
				weaponId = config.Id
			}
		end
	end

	-- 尝试从存储中加载玩家弹药数据
	local success, loadedData = pcall(function()
		-- 这里可以添加从数据存储服务加载数据的代码
		-- 暂时使用默认数据
		return nil
	end)

	-- 如果加载成功，使用加载的数据
	if success and loadedData then
		for ammoId, ammoData in pairs(loadedData) do
			if ammoInventory[ammoId] then
				ammoInventory[ammoId].amount = ammoData.amount or 0
			end
		end
	else
		-- 初始化时所有弹药数量都为0，等待玩家拾取新手盒子后再更新
		for ammoId, ammoData in pairs(ammoInventory) do
			ammoData.amount = 0
		end
		print("初始化弹药库，所有弹药数量设为0，等待拾取新手盒子")
	end

	-- 保存到内存中
	self.PlayerAmmoInventories[userId] = ammoInventory

	-- 同步到客户端
	self:SyncPlayerAmmoInventory(player)

	print("已初始化玩家弹药库: " .. player.Name)
end

-- 保存玩家弹药库
function AmmoInventoryServer:SavePlayerAmmoInventory(player)
	if not player then return end

	local userId = player.UserId
	local ammoInventory = self.PlayerAmmoInventories[userId]

	if not ammoInventory then return end

	-- 这里可以添加保存到数据存储服务的代码
	-- 暂时只打印日志
	print("保存玩家弹药库: " .. player.Name)
end

-- 同步玩家弹药库到客户端
function AmmoInventoryServer:SyncPlayerAmmoInventory(player)
	if not player then return end

	local userId = player.UserId
	local ammoInventory = self.PlayerAmmoInventories[userId]

	if not ammoInventory then
		self:InitializePlayerAmmoInventory(player)
		ammoInventory = self.PlayerAmmoInventories[userId]
	end

	-- 发送同步通知
	NotifyService.FireClient("AmmoInventorySync", player, {
		inventory = ammoInventory
	})

	print("已同步玩家弹药库到客户端: " .. player.Name)
end

-- 获取玩家弹药数量
function AmmoInventoryServer:GetPlayerAmmoAmount(player, ammoId)
	if not player then 
		print("GetPlayerAmmoAmount: 玩家不存在")
		return 0 
	end

	if not ammoId then
		print("GetPlayerAmmoAmount: 弹药ID为nil")
		return 0
	end

	local userId = player.UserId
	local ammoInventory = self.PlayerAmmoInventories[userId]

	if not ammoInventory then
		print("玩家弹药库未初始化，正在初始化: " .. player.Name)
		self:InitializePlayerAmmoInventory(player)
		ammoInventory = self.PlayerAmmoInventories[userId]
	end

	if ammoInventory and ammoInventory[ammoId] then
		local amount = ammoInventory[ammoId].amount
		print("获取玩家弹药数量: " .. player.Name .. ", ID=" .. ammoId .. ", 数量=" .. amount)
		return amount
	end

	print("未找到弹药ID: " .. ammoId .. "，返回0")
	return 0
end

-- 设置玩家弹药数量
function AmmoInventoryServer:SetPlayerAmmoAmount(player, ammoId, amount)
	if not player then return end

	local userId = player.UserId
	local ammoInventory = self.PlayerAmmoInventories[userId]

	if not ammoInventory then
		self:InitializePlayerAmmoInventory(player)
		ammoInventory = self.PlayerAmmoInventories[userId]
	end

	if ammoInventory and ammoInventory[ammoId] then
		-- 确保数量不为负数
		amount = math.max(0, amount)

		-- 更新数量
		ammoInventory[ammoId].amount = amount

		-- 通知客户端
		NotifyService.FireClient("AmmoInventoryUpdate", player, {
			ammoId = ammoId,
			amount = amount
		})

		print("已设置玩家弹药数量: " .. player.Name .. ", ID=" .. ammoId .. ", 数量=" .. amount)
		return true
	end

	return false
end

-- 增加玩家弹药数量
function AmmoInventoryServer:AddPlayerAmmo(player, ammoId, amount)
	if not player or not ammoId or not amount then return false end

	local userId = player.UserId
	local ammoInventory = self.PlayerAmmoInventories[userId]

	if not ammoInventory then
		self:InitializePlayerAmmoInventory(player)
		ammoInventory = self.PlayerAmmoInventories[userId]
	end

	if ammoInventory and ammoInventory[ammoId] then
		-- 增加弹药数量
		ammoInventory[ammoId].amount = ammoInventory[ammoId].amount + amount

		-- 通知客户端
		NotifyService.FireClient("AmmoInventoryUpdate", player, {
			ammoId = ammoId,
			amount = ammoInventory[ammoId].amount
		})

		print("已增加玩家弹药数量: " .. player.Name .. ", ID=" .. ammoId .. ", 增加=" .. amount .. ", 当前=" .. ammoInventory[ammoId].amount)
		return true
	else
		warn("尝试增加不存在的弹药ID: " .. ammoId .. "，请先初始化弹药库")
		return false
	end
end



-- 减少玩家弹药数量
function AmmoInventoryServer:RemovePlayerAmmo(player, ammoId, amount)
	if not player or amount <= 0 then return false end

	local currentAmount = self:GetPlayerAmmoAmount(player, ammoId)

	if currentAmount >= amount then
		return self:SetPlayerAmmoAmount(player, ammoId, currentAmount - amount)
	end

	return false
end

-- 检查玩家是否有足够的弹药
function AmmoInventoryServer:HasEnoughAmmo(player, ammoId, amount)
	local currentAmount = self:GetPlayerAmmoAmount(player, ammoId)
	return currentAmount >= amount
end

-- 根据武器ID获取对应的弹药ID
function AmmoInventoryServer:GetAmmoIdByWeaponId(weaponId)
	for _, config in ipairs(RemoteWeaponConfig) do
		if config.Id == weaponId then
			return config.BallisticId
		end
	end
	return nil
end

-- 从新手盒子物品数据更新弹药数量（拾取新手盒子时调用）
-- 修复：现在接收实际的物品数据，而不是总是使用默认配置
function AmmoInventoryServer:UpdateAmmoFromNewPlayerBox(player, itemsData)
	if not player then
		warn("UpdateAmmoFromNewPlayerBox: 玩家参数为空")
		return false
	end

	-- 确保玩家弹药库已初始化
	self:InitializePlayerAmmoInventory(player)

	-- 如果没有提供物品数据，则使用默认配置（向后兼容）
	local boxItems = itemsData
	if not boxItems then
		warn("UpdateAmmoFromNewPlayerBox: 未提供物品数据，使用默认配置")
		local success, NewPlayerBoxConfig = pcall(function()
			return require(ReplicatedStorage.Scripts.Config.NewPlayerBoxConfig)
		end)

		if not success or not NewPlayerBoxConfig then
			warn("无法加载新手盒子配置")
			return false
		end

		boxItems = NewPlayerBoxConfig.GetItems()
	end

	local bulletAmounts = {}

	-- 收集所有子弹类型及其数量
	for _, item in ipairs(boxItems) do
		if item.itemType == 6 then -- ItemType 6 = 子弹
			-- 将ItemConfig中的子弹ID映射到弹药库中的弹药ID
			local ammoId = self:MapBulletItemToAmmoId(item.id)
			if ammoId then
				bulletAmounts[ammoId] = (bulletAmounts[ammoId] or 0) + item.quantity
				print("从新手盒子获取弹药: AmmoID=" .. ammoId .. ", 数量=" .. item.quantity)
			end
		end
	end

	-- 更新玩家弹药库
	local userId = player.UserId
	local ammoInventory = self.PlayerAmmoInventories[userId]

	if not ammoInventory then
		warn("玩家弹药库未初始化: " .. player.Name)
		return false
	end

	-- 添加新手盒子中的弹药到弹药库
	for ammoId, amount in pairs(bulletAmounts) do
		if ammoInventory[ammoId] then
			-- 增加弹药数量（而不是设置）
			ammoInventory[ammoId].amount = ammoInventory[ammoId].amount + amount

			-- 通知客户端更新
			NotifyService.FireClient("AmmoInventoryUpdate", player, {
				ammoId = ammoId,
				amount = ammoInventory[ammoId].amount
			})

			print("更新玩家弹药库: " .. player.Name .. ", AmmoID=" .. ammoId .. " 增加 " .. amount .. "发，当前总数: " .. ammoInventory[ammoId].amount)
		end
	end

	-- 如果没有子弹数据，打印调试信息
	if next(bulletAmounts) == nil then
		print("新手盒子中没有子弹数据，弹药库保持原状: " .. player.Name)
	end

	return true
end

-- 将子弹ItemID映射到弹药库AmmoID
function AmmoInventoryServer:MapBulletItemToAmmoId(bulletItemId)
	-- 根据更新后的配置表，现在直接使用ItemID作为弹药ID
	-- 这样可以保持一致性，避免复杂的映射关系

	-- 验证这是一个有效的子弹ItemID
	local success, ItemConfig = pcall(function()
		return require(ReplicatedStorage.Scripts.Config.ItemConfig)
	end)

	if not success or not ItemConfig then
		warn("无法加载ItemConfig")
		return nil
	end

	-- 查找子弹物品信息，确认它是子弹类型
	local bulletItem = nil
	for _, item in ipairs(ItemConfig) do
		if item.Id == bulletItemId and item.ItemType == 6 then -- ItemType 6 = 子弹
			bulletItem = item
			break
		end
	end

	if not bulletItem then
		warn("未找到子弹物品配置或物品不是子弹类型: ID=" .. bulletItemId)
		return nil
	end

	-- 直接使用ItemID作为弹药ID
	print("映射子弹到弹药: " .. bulletItem.Chinese .. " -> AmmoID=" .. bulletItemId)
	return bulletItemId
end

-- 根据子弹ID获取子弹名称
function AmmoInventoryServer:GetBulletNameById(bulletId)
	local success, ItemConfig = pcall(function()
		return require(ReplicatedStorage.Scripts.Config.ItemConfig)
	end)

	if not success or not ItemConfig then
		return nil
	end

	for _, item in ipairs(ItemConfig) do
		if item.Id == bulletId and item.ItemType == 6 then
			return item.Chinese
		end
	end

	return nil
end

return AmmoInventoryServer