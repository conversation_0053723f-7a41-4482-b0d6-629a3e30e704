local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players 			= game:GetService("Players")
local UIS 				= game:GetService("UserInputService")
local RunService 		= game:GetService("RunService")
local TweenService		= game:GetService("TweenService")
local TillController 	= require(ReplicatedStorage.Scripts.Client.Controller.TillController)
local Sell 				= require(ReplicatedStorage.Scripts.ItemInteraction.SellManager)
local NotifyManager 	= require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local ProtocolManager 	= require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local TrainEntity 		= require(ReplicatedStorage.Scripts.Server.Manager.TrainManager.TrainEntity)
local ObjectTracker 	= require(ReplicatedStorage.Scripts.ItemInteraction.ObjectTracker) 
local GUIDGenerator 	= require(ReplicatedStorage.Scripts.ItemInteraction.GUIDGenerator)
local ItemUI            = require(ReplicatedStorage.Scripts.ItemUI.ItemUI)
local EquipManager		= require(ReplicatedStorage.Scripts.Equip.EquipManager)
local PlayerDownedClient = require(ReplicatedStorage.Scripts.Client.Services.PlayerDownedClient)
local AmmoInventoryService = require(ReplicatedStorage.Scripts.Client.Services.AmmoInventoryService)
local ItemVisableUI		= ReplicatedStorage.UI.ItemVisableUI
local ItemInteractionSystem = {}

-- 常量 
local MIN_HOLD_DISTANCE 	= 5
local MAX_HOLD_DISTANCE 	= 15
local DEFAULT_HOLD_DISTANCE = 5
local ROTATION_SPEED 		= 1      -- 单次旋转弧度增量
local ROTATION_DIRECTION 	= -1 	 -- 反转旋转方向，符合直觉

local SPHERE_RADIUS 		= 2      -- 区域检测半径
local COLLISION_OFFSET 		= 0.1    -- 碰撞偏移量
local GROUND_RAY_LENGTH 	= 50     -- 地面检测射线长度
local OBJECT_HEIGHT_FACTOR 	= 0.5    -- 物体高度系数
local BASEPARTWELDMULTIPLY	= 0.5
local MODELWELDMULTIPLY		= 1.5
local currentObject 		= nil
local MaxWeaponCount        = 10
local currentWeaponCount	= 0
-- 旋转轴相关配置
local ROTATION_AXIS_VISUALIZATION = true -- 控制是否启用旋转轴可视化
local AXIS_LENGTH = 1                   -- 旋转轴长度
local AXIS_THICKNESS = 0.05             -- 旋转轴粗细
local AXIS_COLORS = {                   -- 各轴颜色
	X = Color3.new(1, 0, 0),            -- 红色表示X轴
	Y = Color3.new(0, 1, 0),            -- 绿色表示Y轴
	Z = Color3.new(0, 0, 1)             -- 蓝色表示Z轴
}
-- 旋转轴对象缓存
local rotationAxes = {}
-- 每玩家状态
local playerStates = {}
local syncedObjects = {} -- 同步物体缓存
local backpack
local tipStr = ""
local isweapon = false
-- 日志工具
local function log(message)
	--print("[ItemSync] " .. tostring(message))
end

-- 获取玩家的UI提示元素
local function getPlayerItemTip(player)
	local playerGui = player:WaitForChild("PlayerGui")
	local startGui = playerGui:FindFirstChild("SatrtGUi")
	backpack = player:FindFirstChild("Backpack")
	if not startGui then return {} end

	local itemTip = startGui:FindFirstChild("ItemTip")
	if not itemTip then return {} end

	local uiElements = {}
	for _, item in ipairs(itemTip:GetChildren()) do
		if item:IsA("ImageLabel") then
			uiElements[item.Name] = item
		end
	end
	return uiElements
end

-- 确保 RotationValues
local function ensureRotationValues(obj)
	if not obj:FindFirstChild("RotationValues") then
		local folder = Instance.new("Folder")
		folder.Name = "RotationValues"
		for _, name in ipairs({"X","Y","Z"}) do
			local nv = Instance.new("NumberValue")
			nv.Name = name
			nv.Value = 0
			nv.Parent = folder
		end
		folder.Parent = obj
		log("为物体添加 RotationValues: " .. obj:GetFullName())
	end

	for _, axis in ipairs({"X", "Y", "Z"}) do
		if not obj.RotationValues:FindFirstChild(axis) then
			local value = Instance.new("NumberValue")
			value.Name = axis
			value.Value = 0
			value.Parent = obj.RotationValues
		end
	end

	return obj.RotationValues
end

-- 确保同步ID存在且有效
local function ensureSyncId(obj)
	if not obj:FindFirstChild("SyncId") then
		log("为物体添加SyncId: " .. obj:GetFullName())
		local id = Instance.new("StringValue")
		id.Name = "SyncId"
		id.Value = GUIDGenerator.generate() 
		id.Parent = obj
	elseif obj.SyncId.Value == "" then
		log("重置无效SyncId: " .. obj:GetFullName())
		obj.SyncId.Value = GUIDGenerator.generate()
	end

	return obj.SyncId.Value
end

-- 判断标签（BoolValue 或 StringValue）
local function hasTag(obj, tagName)
	if obj:FindFirstChild(tagName) then
		return true
	end

	local mdl = obj:FindFirstAncestorOfClass("Model")
	if mdl and mdl.PrimaryPart and mdl.PrimaryPart:FindFirstChild(tagName) then
		return true
	end

	local tool = obj:FindFirstAncestorOfClass("Tool") 
	if tool and tool:FindFirstChild(tagName) then
		return true
	end

	local accessory = obj:FindFirstAncestorOfClass("Accessory") 
	if accessory and accessory:FindFirstChild(tagName) then
		return true
	end

	return false
end
-- 清除旋转轴
local function clearRotationAxes()
	for _, axis in pairs(rotationAxes) do
		if axis and axis:IsDescendantOf(workspace) then
			axis:Destroy()
		end
	end
	rotationAxes = {}
end

-- 绘制旋转轴
local function drawRotationAxis(state, axisName)
	clearRotationAxes()

	if not state.heldObject or not ROTATION_AXIS_VISUALIZATION then
		return
	end

	local obj = state.heldObject
	local objModel = obj:FindFirstAncestorOfClass("Model")
	local baseCFrame = obj.CFrame

	if objModel and objModel.PrimaryPart then
		baseCFrame = objModel.PrimaryPart.CFrame
	end

	-- 创建当前选择的旋转轴
	local axis = Instance.new("Part")
	axis.Name = "AxisName"
	axis.Anchored = true
	axis.CanTouch = false
	axis.CanQuery = false
	axis.Massless = true
	axis.CanCollide = false
	axis.Material = Enum.Material.Neon
	axis.Color = AXIS_COLORS[axisName]
	axis.Transparency = 0.3

	if axisName == "X" then
		axis.Size = Vector3.new(AXIS_LENGTH, AXIS_THICKNESS, AXIS_THICKNESS)
		axis.CFrame = baseCFrame * CFrame.new(AXIS_LENGTH/2, 0, 0)
	elseif axisName == "Y" then
		axis.Size = Vector3.new(AXIS_THICKNESS, AXIS_LENGTH, AXIS_THICKNESS)
		axis.CFrame = baseCFrame * CFrame.new(0, AXIS_LENGTH/2, 0)
	elseif axisName == "Z" then
		axis.Size = Vector3.new(AXIS_THICKNESS, AXIS_THICKNESS, AXIS_LENGTH)
		axis.CFrame = baseCFrame * CFrame.new(0, 0, AXIS_LENGTH/2)
	end

	axis.Parent = workspace.Terrain
	rotationAxes[axisName] = axis
end
-- 高亮效果：创建和更新高亮
local function createHighlight(state, part)
	if state.highlight then state.highlight:Destroy() end

	local highlight = Instance.new("Highlight")

	if part:FindFirstAncestorOfClass("Model") then
		local model = part:FindFirstAncestorOfClass("Model")
		if model.PrimaryPart then
			highlight.Adornee = model.PrimaryPart
		else
			highlight.Adornee = part
		end
	else
		highlight.Adornee = part
	end

	highlight.FillTransparency = 1
	highlight.OutlineTransparency = 0.2
	highlight.FillColor = Color3.fromRGB(255, 220, 0)
	highlight.OutlineColor = Color3.fromRGB(255, 165, 0)
	highlight.Parent = highlight.Adornee
	state.highlight = highlight
end
-- 物品提示效果
local function creaeItemUI(state,part)
	-- 先销毁旧的UI
	if state.itemVisibleUI then 
		state.itemVisibleUI:Destroy()
	end

	-- 克隆UI模板
	local itemVisibleUI = ItemVisableUI:Clone()
	local nameUi = itemVisibleUI:FindFirstChild("Name")
	local typeUi = itemVisibleUI:FindFirstChild("Type")

	-- 获取物品属性
	local name = part:FindFirstChild("Name")
	local itemtype = part:FindFirstChild("ItemType")
	local itemQuality = part:FindFirstChild("ItemQuality")

	itemVisibleUI.Parent = part

	if name and itemtype and itemQuality then
		-- 设置名称
		nameUi.Text = name.Value
		print("Setting name:", name.Value) -- 调试输出

		-- 设置类型文本
		local typeText = ""
		if itemtype.Value == 2 then
			typeText = "特殊货币"
		elseif itemtype.Value == 3 then
			typeText = "装饰品"
		elseif itemtype.Value == 4 then
			typeText = "防御设施"
		elseif itemtype.Value == 5 then
			typeText = "照明设备"
		elseif itemtype.Value == 6 then
			typeText = "子弹"
		elseif itemtype.Value == 7 then
			typeText = "药物"
		elseif itemtype.Value == 8 then
			typeText = "爆炸物"
		elseif itemtype.Value == 9 or itemtype.Value == 10 then
			typeText = "武器"
		elseif itemtype.Value == 11 then
			typeText = "装备"
		end

		typeUi.Text = typeText
		print("Setting type:", typeText, "for type ID:", itemtype.Value) -- 调试输出

		-- 设置品质颜色
		if itemQuality.Value == 1 then			
			typeUi.TextColor3 = Color3.new(0, 0.666667, 1)
		elseif itemQuality.Value == 2 then
			typeUi.TextColor3 = Color3.new(0.666667, 0, 1)
		elseif itemQuality.Value == 3 then
			typeUi.TextColor3 = Color3.new(1, 1, 0.498039)
		elseif itemQuality.Value == 4 then
			typeUi.TextColor3 = Color3.new(1, 0, 0)
		else
			typeUi.TextColor3 = Color3.new(0, 0, 0)
		end
	else
		print("Missing required item attributes!")
	end

	state.itemVisibleUI = itemVisibleUI
end
-- 清除高亮
local function clearHighlight(state)
	if state.highlight then
		state.highlight:Destroy()
		state.highlight = nil
	end
end
-- 清除Ui
local function clearItemUI(state)
	if state.itemVisibleUI then
		state.itemVisibleUI:Destroy()
		state.itemVisibleUI = nil
	end
end
-- 射线检测（中心屏幕）
local function raycastCenter(camera, maxDist, ignoreList)
	local vp = camera.ViewportSize
	local ray = camera:ViewportPointToRay(vp.X/2, vp.Y/2)
	local params = RaycastParams.new()
	params.FilterType = Enum.RaycastFilterType.Exclude
	params.FilterDescendantsInstances = ignoreList or {}
	local res = workspace:Raycast(ray.Origin, ray.Direction * maxDist, params)

	if res then
		return res.Instance, res.Position, res.Normal
	else
		return nil, ray.Origin + ray.Direction * maxDist, Vector3.new(0, 1, 0)
	end
end

-- 多方式检测目标
local function detectReleaseTarget(camera, maxDist, ignoreList, object)
	-- 1. 中心射线
	local hit, pos, normal = raycastCenter(camera, maxDist, ignoreList)

	if hit and (hasTag(hit, "CargoFloor") or hasTag(hit, "FuelTank") or hasTag(hit, "Shop") or hasTag(hit, "Sell")) then
		return hit, pos, normal
	end

	-- 2. 向下射线（防止俯视角无法命中）
	local downParams = RaycastParams.new()
	downParams.FilterType = Enum.RaycastFilterType.Exclude
	downParams.FilterDescendantsInstances = ignoreList or {}
	local downRes = workspace:Raycast(object.Position, Vector3.new(0, -maxDist, 0), downParams)

	if downRes and (hasTag(downRes.Instance, "CargoFloor") or hasTag(downRes.Instance, "FuelTank") or hasTag(downRes.Instance, "Shop") or hasTag(downRes.Instance, "Sell")) then
		return downRes.Instance, downRes.Position, downRes.Normal
	end

	-- 3. 球形检测
	local sphereRadius = maxDist -- 球形检测半径
	local overlapParams = OverlapParams.new()
	overlapParams.FilterType = Enum.RaycastFilterType.Exclude
	overlapParams.FilterDescendantsInstances = ignoreList or {}

	-- 创建临时Part作为检测范围
	local tempSphere = Instance.new("Part")
	tempSphere.Shape = Enum.PartType.Ball
	tempSphere.Size = Vector3.new(sphereRadius,sphereRadius,sphereRadius) -- 直径 = 半径*2
	tempSphere.Position = object.Position
	tempSphere.Transparency = 1
	tempSphere.Anchored = true
	tempSphere.CanCollide = false
	tempSphere.Parent = workspace.Terrain -- 临时父级

	-- 获取球体范围内的所有部件
	local partsInSphere = workspace:GetPartsInPart(tempSphere, overlapParams)

	-- 清理临时Part
	tempSphere:Destroy()

	-- 查找距离最近的有效目标
	local closestPart = nil
	local closestDistance = math.huge
	local closestPosition = nil
	local closestNormal = nil

	for _, part in ipairs(partsInSphere) do
		if hasTag(part, "CargoFloor") or hasTag(part, "FuelTank") or hasTag(part, "Shop") or hasTag(part, "Sell") then
			local distance = (part.Position - object.Position).Magnitude
			if distance < closestDistance then
				closestDistance = distance
				closestPart = part
				closestPosition = part.Position
				closestNormal = Vector3.new(0, 1, 0) -- 默认向上的法线，可根据需要调整
			end
		end
	end

	return closestPart or hit, closestPosition or pos, closestNormal or normal
end

-- 初始化玩家状态
local function initPlayerState(player)
	playerStates[player] = {
		heldObject = nil,
		currentHoldDistance = DEFAULT_HOLD_DISTANCE,
		highlight = nil,
		lastTarget = nil,
		rotAxis = 0,    -- 0=Y,1=X,2=Z
		originalDistance = DEFAULT_HOLD_DISTANCE,
		syncLoop = nil,
		uiElements = getPlayerItemTip(player)
	}
end

-- 获取玩家状态
local function getPlayerState(player)
	if not playerStates[player] then
		log("初始化玩家状态: " .. player.Name)
		initPlayerState(player)
	end

	return playerStates[player]
end

-- 定期同步位置
local function startSyncLoop(state)
	if state.syncLoop and coroutine.status(state.syncLoop) ~= "dead" then
		coroutine.close(state.syncLoop)
	end

	state.syncLoop = coroutine.create(function()
		while state.heldObject and state.heldObject:IsDescendantOf(workspace) do
			task.wait(0.1)

			if state.heldObject and state.heldObject:IsDescendantOf(workspace) then
				ProtocolManager.SendMessage("ItemMoveEvent", {
					objectId = state.heldObject.SyncId.Value,
					position = state.heldObject.Position
				})
			end
		end

		state.syncLoop = nil
	end)

	coroutine.resume(state.syncLoop)
end

-- 普通释放
local function releaseObject(state)
	local obj = state.heldObject
	if obj then
		-- 若持有对象是模型的一部分，找到其所属模型
		local model = obj:FindFirstAncestorOfClass("Model")
		if model then
			-- 遍历模型所有子部件，重置状态
			for _, part in ipairs(model:GetChildren()) do
				if part:IsA("BasePart") then
					part.Anchored = false  -- 取消锚定
					part.CanCollide = true  -- 恢复碰撞
				end
			end
		else
			-- 单个部件的处理
			obj.Anchored = false
			obj.CanCollide = true
		end
	end

	state.heldObject = nil
	currentObject = state.heldObject
	state.currentHoldDistance = DEFAULT_HOLD_DISTANCE

	if state.syncLoop then
		coroutine.close(state.syncLoop)
		state.syncLoop = nil
	end

	-- 隐藏所有提示UI
	for name, element in pairs(state.uiElements) do
		element.Visible = false
	end
	if obj:FindFirstChild("InTill") then
		ProtocolManager.SendMessage("ConsumptionResetEvent",{obj = obj})
	end
end
local function sendRelease(obj)
	if not obj then return end

	ObjectTracker.unregisterObject(obj)
	local objectId = obj.SyncId.Value
	local releasePosition = obj.Position -- 记录释放时的实际位置
	log("释放物体: " .. objectId .. " 位置: " .. tostring(releasePosition))

	-- 发送释放事件时，明确携带释放位置
	ProtocolManager.SendMessage("ItemReleaseEvent", {
		objectId = objectId,
		position = releasePosition, -- 确保发送的是释放时的位置
		rotation = {
			X = obj.RotationValues.X.Value,
			Y = obj.RotationValues.Y.Value,
			Z = obj.RotationValues.Z.Value
		}
	})

	-- 客户端本地先强制更新位置（避免等待服务器同步的延迟）
	obj.Position = releasePosition
end
-- 创建约束
local function createWeld(obj, target)
	if not obj or not target then
		return false
	end

	-- 查找可焊接的部件
	local objPart = obj:IsA("BasePart") and obj or obj:FindFirstChildWhichIsA("BasePart")
	local targetPart = target:IsA("BasePart") and target or target:FindFirstChildWhichIsA("BasePart")

	if not objPart or not targetPart then
		return false
	end
	if obj:FindFirstAncestorOfClass("Model") then		
		local model = obj:FindFirstAncestorOfClass("Model")
		for _, part in ipairs(model:GetDescendants()) do
			if part:IsA("BasePart") then
				part.Anchored = false
				part.CanCollide = true
			end
		end
	else
		obj.Anchored = false
		obj.CanCollide = true
	end
	task.wait(0.1) --等待1帧以上

	-- 创建焊接约束
	local weld = Instance.new("WeldConstraint")
	weld.Name = "creatWeld"
	weld.Part0 = targetPart
	weld.Part1 = objPart
	weld.Parent = objPart
	return true
end

-- 移除焊接约束
local function removeWelds(obj)
	if not obj then
		return
	end

	if obj:FindFirstAncestorOfClass("Model") then
		local primaryPart = obj.Parent.PrimaryPart
		if primaryPart then
			local weld = primaryPart:FindFirstChild("creatWeld")
			if weld then
				weld:Destroy()
			end
		end
	else
		local weld = obj:FindFirstChild("creatWeld")
		if weld then
			weld:Destroy()
		end
	end

end

-- 请求焊接
local function requestWeld(objectId, targetId)
	log("焊接物体: " .. objectId,"->",targetId)

	ProtocolManager.SendMessage("ItemWeldEvent", {
		objectId = objectId,
		targetId = targetId
	})
end

-- 请求解除焊接
local function requestUnweld(objectId)
	log("释放物体: " .. objectId)

	ProtocolManager.SendMessage("ItemUnweldEvent", {
		objectId = objectId
	})
end

-- 抓取
local function grabObject(state, part)
	if state.heldObject then 
		log("已有持有物体，无法抓取")
		return 
	end
	-- 检查物体是否存在 Grabbable 标签
	if not hasTag(part, "Grabbable") then 
		log("物体不可抓取: " .. part:GetFullName())
		return 
	end
	local model = part:FindFirstAncestorOfClass("Model")
	if model then
		local canGrab = model.PrimaryPart:FindFirstChild("Grabbable")
		if canGrab and canGrab.Value == true then
			log("物品已经被抓取")
			return
		end
	else
		local canGrab = part:FindFirstChild("Grabbable")
		if canGrab and canGrab.Value == true then
			log("物品已经被抓取")
			return
		end
	end
	clearItemUI(state)
	
	-- 移除所有焊接约束
	local objectId
	if model then
		objectId = ensureSyncId(model.PrimaryPart)
	else
		objectId = ensureSyncId(part)
	end
	requestUnweld(objectId)
	task.wait(0.1)
	if model then		
		for _, childPart in ipairs(model:GetChildren()) do
			if childPart:IsA("BasePart") then
				childPart.Anchored = true
				childPart.CanCollide = false
			end
		end

		if model.PrimaryPart then
			state.heldObject = model.PrimaryPart
		end
	else
		state.heldObject = part
		currentObject = state.heldObject
		part.Anchored = true
		part.CanCollide = false
	end

	-- 注册对象到跟踪器
	ObjectTracker.registerObject(state.heldObject, objectId)

	-- 准备抓取前的状态
	createHighlight(state, state.heldObject)	
	
	local inTill = part:FindFirstChild("InTill")
	if inTill and inTill.Value == true then -- 物品是商品并且在收银台
		-- 从收银台移除物品
		ProtocolManager.SendMessage("RemoveCashierEvent", {objectId = objectId})
		TillController.setCanCollide(true)
	elseif inTill and inTill.Value == false then -- 
		TillController.setCanCollide(true)
	end

	

	-- 确保旋转值存在
	ensureRotationValues(state.heldObject)
	-- 记录当前位置作为原始位置
	local camPos = workspace.CurrentCamera.CFrame.Position
	local objPos = state.heldObject.Position
	state.originalDistance = math.clamp((objPos - camPos).Magnitude, MIN_HOLD_DISTANCE, MAX_HOLD_DISTANCE)
	state.currentHoldDistance = state.originalDistance

	-- 位置同步开启
	startSyncLoop(state)
	local object = ObjectTracker.findObjectById(objectId)
	object.RotationValues.X.Value = 0
	object.RotationValues.Y.Value = 0
	object.RotationValues.Z.Value = 0
	-- 发送抓取事件
	ProtocolManager.SendMessage("ItemGrabEvent", {
		objectId = objectId,
		playerId = Players.LocalPlayer.UserId,
		rotation = {
			object.RotationValues.X.Value,
			object.RotationValues.Y.Value,
			object.RotationValues.Z.Value 
		}
	})
end
local function uiTipVisable(state)
	for name, element in pairs(state.uiElements) do
		element.Visible = false
	end
end
local function updateWeaponTip(weapon,state)
	if not state.uiElements then return end
	-- 先隐藏所有提示
	uiTipVisable(state)
	-- 根据物品类型显示基础提示
	local typeValue = weapon.Handle:FindFirstChild("ItemType") and weapon.Handle:FindFirstChild("ItemType").Value
	if typeValue == 9 then
		if state.uiElements.Mouse then
			state.uiElements.Mouse.Visible = true
			local text = state.uiElements.Mouse:FindFirstChild("TextLabel")
			if text then text.Text = "攻击" end
		end
	end
	if typeValue == 10 then
		if state.uiElements.Mouse then
			state.uiElements.Mouse.Visible = true
			local text = state.uiElements.Mouse:FindFirstChild("TextLabel")
			if text then text.Text = "攻击" end
		end
		if state.uiElements.R then
			state.uiElements.R.Visible = true
			local text = state.uiElements.R:FindFirstChild("TextLabel")
			if text then text.Text = "换弹" end
		end
	end
end
-- 物品交互处理
local function handleItemInteraction(obj, state, interactionType)
	if not obj then
		warn("无效的物品对象")
		return false
	end
	--local consumptionQuantity = obj:FindFirstChild("ConsumptionQuantity")
	--if consumptionQuantity then
	--	warn("未购买，无法装备")
	--	return
	--end
	local itemType = obj:FindFirstChild("ItemType") and obj:FindFirstChild("ItemType").Value

	if interactionType == "bag" then
		if itemType == 9 or itemType == 2 or itemType == 10 or itemType == 7 then
			warn("该物品不能放入背包")
			return false
		end

		local textureId = obj:FindFirstChild("Icon")
		local nameString = obj:FindFirstChild("Name")
		local itemId = obj:FindFirstChild("Id")
		local itemType = obj:FindFirstChild("ItemType")
		if textureId and nameString and itemId and itemType then
			local icon = textureId.Value
			local name = nameString.Value
			local id = itemId.Value
			local typeNum = itemType.Value
			if id and icon and name and ItemUI.updateItemUI(id, icon, name,typeNum) then
				uiTipVisable(state)
				local objectId = obj.SyncId.Value
				ProtocolManager.SendMessage("ItemDestroyEvent", {objectId = objectId})
				state.heldObject = nil
				clearRotationAxes()
				return true
			end
		end
	elseif interactionType == "equip" then
		if itemType == 11 and obj:FindFirstAncestorOfClass("Accessory") then
			uiTipVisable(state)
			ProtocolManager.SendMessage("SetEquipEvent", {obj = obj})
			state.heldObject = nil
			clearRotationAxes()
			return true
		elseif (itemType == 9 or itemType == 10 or itemType == 7) and obj:FindFirstAncestorOfClass("Tool") then
			if currentWeaponCount >= MaxWeaponCount then return end
			local tool = obj.Parent
			if tool:IsA("Tool") then
				clearHighlight(state)
				isweapon = true
				updateWeaponTip(tool,state)
				local humanoid = Players.LocalPlayer.Character:FindFirstChildOfClass("Humanoid")
				ProtocolManager.SendMessage("ToolEquipEvent", {tool = tool, handle = obj})
				obj.Anchored = false
				state.heldObject = nil
				clearRotationAxes()
				currentWeaponCount += 1
				return true
			end	
		elseif itemType == 2 then			
			ProtocolManager.SendMessage("SetCurrencyEvent", {count = 1})
			uiTipVisable(state)
			local objectId = obj.SyncId.Value
			ProtocolManager.SendMessage("ItemDestroyEvent", {objectId = objectId})
			clearRotationAxes()
			state.heldObject = nil
			return true
		elseif itemType ==6 then
			--子弹类型添加到弹药库
			local itemId = obj:FindFirstChild("Id")
			local nameString = obj:FindFirstChild("Name")
			
			if itemId and nameString then
				local bulletId = itemId.Value
				local bulletName = nameString.Value
				
				-- 获取子弹数量（默认为30）
				local quantity = 30
				local quantityValue = obj:FindFirstChild("Quantity")
				if quantityValue then
					quantity = quantityValue.Value
				end

				print("尝试添加子弹到弹药库: ID=" .. bulletId .. ", 名称=" .. bulletName .. ", 数量=" .. quantity)

				-- 通知服务端添加子弹到弹药库
				ProtocolManager.SendMessage("AddBulletToAmmoInventoryEvent", {
					bulletId = bulletId,
					quantity = quantity,
					bulletName = bulletName
				})
				
				-- 销毁物品
				uiTipVisable(state)
				local objectId = obj.SyncId.Value
				ProtocolManager.SendMessage("ItemDestroyEvent", {objectId = objectId})
				state.heldObject = nil
				clearRotationAxes()
				print("子弹已添加到弹药库: " .. bulletName)
				return true
			else
				warn("子弹物品缺少必要属性（Id或Name）")
				return false
				
			end
		end
		
	end

	return false
end

-- 物品放进背包
local function itemToBag(obj, state)
	return handleItemInteraction(obj, state, "bag")
end

-- 装备
local function itemToEquip(obj, state)
	return handleItemInteraction(obj, state, "equip")
end

-- 释放处理
local function handleRelease(state, player)
	local obj = state.heldObject
	if not obj then 
		log("无持有物体，无法释放")
		return 
	end
	-- 清除旋转轴
	clearRotationAxes()
	local cam = workspace.CurrentCamera
	local ignore = { player.Character, obj }
	local hitRange 

	if obj:FindFirstAncestorOfClass("Model") then
		hitRange = DEFAULT_HOLD_DISTANCE * MODELWELDMULTIPLY
	else
		hitRange = obj.Size.Magnitude * BASEPARTWELDMULTIPLY
	end
	local target, targetPos, targetNormal = detectReleaseTarget(cam, hitRange, ignore, obj)
	if obj:FindFirstChild("InTill") then
		TillController.setCanCollide(false)
	end
	if target then
		-- 1. 燃料→油箱
		if hasTag(obj, "CombustionValue") and hasTag(target, "FuelTank") then
			print("燃料:", obj:GetFullName())
			local fuel = obj:FindFirstChild("CombustionValue")
			if fuel then
				uiTipVisable(state)
				local fuelNum = fuel.Value
				ProtocolManager.SendMessage("FuelEvent", {fuelNum = fuelNum})
				local objectId = obj.SyncId.Value
				ProtocolManager.SendMessage("ItemDestroyEvent", {objectId = objectId})
				state.heldObject = nil
				return
			end
		end

		-- 2. 可固定→车厢地板
		if hasTag(obj, "Attachable") and hasTag(target, "CargoFloor") then
			local itemPart = obj:IsA("BasePart") and obj or obj:FindFirstChildWhichIsA("BasePart")
			if itemPart then
				uiTipVisable(state)
				local objId = itemPart.SyncId.Value
				ProtocolManager.SendMessage("ItemMoveEvent", {objId = objId})
				sendRelease(obj)
				local targetID = target.SyncId.Value
				requestWeld(objId, targetID)
				-- 统一获取位置（Vector3）和旋转（弧度值，Vector3）
				local position, rotation
				local model = obj:FindFirstAncestorOfClass("Model")
				if model then
					-- 模型：从 CFrame 中提取位置和旋转（转为弧度）
					local cframe = model:GetPrimaryPartCFrame()
					position = cframe.Position  -- Vector3 位置
					local x, y, z = cframe:ToEulerAnglesXYZ()  -- 弧度值
					rotation = Vector3.new(x, y, z)
				else
					-- 单个部件：位置直接取 Position，旋转角度转为弧度
					position = obj.Position  -- Vector3 位置
					-- 将角度（度）转为弧度
					rotation = Vector3.new(
						math.rad(obj.Rotation.X),
						math.rad(obj.Rotation.Y),
						math.rad(obj.Rotation.Z)
					)
				end

				-- 发送统一格式的数据
				ProtocolManager.SendMessage("ItemWeldPosEvent", {
					objectId = objId,
					position = position,  -- 始终为 Vector3
					rotation = rotation   -- 始终为弧度值的 Vector3
				})
			end

			print("固定到地板:", obj:GetFullName(), "→", target:GetFullName())
			state.heldObject = nil
			return
		end

		--3. 收银台
		if hasTag(obj, "ConsumptionQuantity") and hasTag(target, "Shop") then
			local itemPart = obj:IsA("BasePart") and obj or obj:FindFirstChildWhichIsA("BasePart")
			if itemPart then
				uiTipVisable(state)
				local objId = itemPart.SyncId.Value
				ProtocolManager.SendMessage("ItemMoveEvent", {objId = objId})
				local targetID = target.SyncId.Value
				requestWeld(objId, targetID)
				sendRelease(obj)
			end

			local objectId = obj.SyncId.Value
			ProtocolManager.SendMessage("AddCashierEvent", {objectId = objectId})
			print("固定到收银台:", obj:GetFullName(), "→", target:GetFullName())
			state.heldObject = nil
			return
		end

		--4. 出售
		if hasTag(obj, "ObtainNumber") and hasTag(target, "Sell") then
			uiTipVisable(state)
			Sell.SellItem(player, obj)
			local objectId = obj.SyncId.Value
			ProtocolManager.SendMessage("ItemDestroyEvent", {objectId = objectId})
			state.heldObject = nil
			print("出售物品:", obj:GetFullName(), "→", target:GetFullName())
			return
		end
	end

	sendRelease(state.heldObject)
	releaseObject(state)
	print("释放:", obj:GetFullName(), "Target:", target and target:GetFullName() or "nil")
end
-- 创建背景闪烁效果
local function createBackgroundFlashEffect(textLabel)
	if not textLabel or not textLabel:IsA("TextLabel") then return end
	-- 查找背景元素（假设背景是TextLabel的父级ImageLabel或自身的BackgroundColor3）
	local background = textLabel

	-- 记录原始背景状态
	local originalColor = background.BackgroundColor3
	local originalTransparency = background.BackgroundTransparency

	-- 停止可能存在的旧动画
	if background:FindFirstChild("BgFlashTween") then
		background.BgFlashTween:Cancel()
	end

	-- 定义背景闪烁效果（0.3秒内从原始状态→高亮→原始状态）
	local tweenInfo = TweenInfo.new(
		0.2, -- 闪烁时长
		Enum.EasingStyle.Quad,
		Enum.EasingDirection.InOut,
		1, -- 只闪烁一次
		false
	)

	-- 闪烁目标（背景变白且半透明）
	local goals = {
		BackgroundColor3 = Color3.new(1, 0.890196, 0.0470588), -- 白色背景
		BackgroundTransparency = 0.5 -- 半透明
	}

	-- 创建并播放动画
	local tween = TweenService:Create(background, tweenInfo, goals)
	tween.Name = "BgFlashTween"
	tween.Parent = background

	-- 动画结束后还原
	tween.Completed:Connect(function()
		background.BackgroundColor3 = originalColor
		background.BackgroundTransparency = originalTransparency
		tween:Destroy()
	end)

	tween:Play()
end
local function laetTipStr(str,text)
	if tipStr ~= str then
		createBackgroundFlashEffect(text)
		tipStr = str
	end
end
-- 获取模型所有子部件（包括嵌套）
local function getModelParts(model)
	local parts = {}
	for _, descendant in ipairs(model:GetDescendants()) do
		if descendant:IsA("BasePart") then
			table.insert(parts, descendant)
		end
	end
	return parts
end
-- 更新物品提示UI
local function updateItemTips(state, obj, targetType)
	if not state.uiElements then return end
	-- 先隐藏所有提示
	uiTipVisable(state)
	-- 根据物品类型显示基础提示
	local typeValue = obj:FindFirstChild("ItemType") and obj:FindFirstChild("ItemType").Value
	if typeValue == 11 then
		if state.uiElements.E then
			state.uiElements.E.Visible = true
			local text = state.uiElements.E:FindFirstChild("TextLabel")
			if text then text.Text = "装备" end
		end
		if state.uiElements.F then
			state.uiElements.F.Visible = true
			local text = state.uiElements.F:FindFirstChild("TextLabel")
			if text then text.Text = "放入背包" end
		end
	elseif typeValue == 9 or typeValue == 10 or typeValue == 7 then
		if state.uiElements.E then
			state.uiElements.E.Visible = true
			local text = state.uiElements.E:FindFirstChild("TextLabel")
			if text then text.Text = "装备" end
		end
	elseif typeValue == 2 then
		if state.uiElements.E then
			state.uiElements.E.Visible = true
			local text = state.uiElements.E:FindFirstChild("TextLabel")
			if text then text.Text = "获取" end
		end
	elseif typeValue == 6 then
		-- 子弹类型：显示添加到弹药库的提示
		if state.uiElements.E then
			state.uiElements.E.Visible = true
			local text = state.uiElements.E:FindFirstChild("TextLabel")
			if text then text.Text = "添加到弹药库" end
		end
	else
		if state.uiElements.F then
			state.uiElements.F.Visible = true
			local text = state.uiElements.F:FindFirstChild("TextLabel")
			if text then text.Text = "放入背包" end
		end	
		if state.uiElements.Z then
			state.uiElements.Z.Visible = true
			local text = state.uiElements.Z:FindFirstChild("TextLabel")
			if text then text.Text = "换轴" end
		end
		if state.uiElements.E then
			state.uiElements.E.Visible = true
			local text = state.uiElements.E:FindFirstChild("TextLabel")
			if text then text.Text = "旋转" end
		end
	end
	-- 如果有目标类型，修改Mouse提示文本
	if targetType and state.uiElements.Mouse and state.heldObject then
		state.uiElements.Mouse.Visible = true
		local text = state.uiElements.Mouse:FindFirstChild("TextLabel")
		if text then
			local fuel = obj:FindFirstChild("CombustionValue")
			local obtainNumber = obj:FindFirstChild("ObtainNumber")
			local consumptionQuantity = obj:FindFirstChild("ConsumptionQuantity")
			if targetType == "FuelTank" and fuel then
				text.Text = "燃料"
				laetTipStr("燃料",text)
			elseif targetType == "CargoFloor" then
				text.Text = "焊接"
				laetTipStr("焊接",text)
			elseif targetType == "Shop" and consumptionQuantity then
				text.Text = "购买"
				laetTipStr("购买",text)
			elseif targetType == "Sell" and obtainNumber then
				text.Text = "出售"
				laetTipStr("出售",text)
			end
		end
	elseif state.uiElements.Mouse and not targetType and state.heldObject then
		state.uiElements.Mouse.Visible = true
		local text = state.uiElements.Mouse:FindFirstChild("TextLabel")
		if text then text.Text = "放置" end
		laetTipStr("放置",text)
	elseif state.uiElements.Mouse and not targetType and not state.heldObject then
		state.uiElements.Mouse.Visible = true
		local text = state.uiElements.Mouse:FindFirstChild("TextLabel")
		if text then text.Text = "拿取" end
		laetTipStr("拿取",text)
	end
end

-- 客户端初始化
function ItemInteractionSystem.initClient()
	log("客户端初始化开始")

	local player = Players.LocalPlayer
	initPlayerState(player)
	local state = getPlayerState(player)
	local camera = workspace.CurrentCamera

	-- 检查关键服务和模块是否正确加载
	if not ProtocolManager or not ProtocolManager.SendMessage then
		warn("错误: ProtocolManager未正确加载")
		return
	end

	backpack = player:FindFirstChild("Backpack")

	if player.Character then
		player.Character.ChildRemoved:Connect(function(child)
			if child:IsA("Tool") then
				local handle = child:FindFirstChild("Handle")

				if handle then
					local isWeapon = player.Character:FindFirstChild("IsWeapon")
					if not isWeapon then
						isWeapon = Instance.new("BoolValue")
						isWeapon.Name = "IsWeapon"
						isWeapon.Parent = player.Character
						isWeapon.Value = false
					else
						isWeapon.Value = false
					end
					isweapon = isWeapon.Value
					uiTipVisable(state)
					local syncId = handle:FindFirstChild("SyncId")
					if syncId and syncId:IsA("StringValue") then
						local objectId = syncId.Value
						-- 发送可抓取事件（丢弃工具后应该允许重新抓取）
						ProtocolManager.SendMessage("ItemCanGrabEvent", {
							objectId = objectId,
							canGrab = false  -- 工具丢弃后应该可以重新抓取
						})
					end
				end

			end
		end)
		player.Character.ChildAdded:Connect(function(child)
			if child:IsA("Tool") then
				if state.heldObject then
					sendRelease(state.heldObject)
					releaseObject(state)
				end
				local toolName = child.Name
				ProtocolManager.SendMessage("ItemCanGrabEvent", {toolName = toolName,canGrab = true})
				local isWeapon = player.Character:FindFirstChild("IsWeapon")
				if not isWeapon then
					isWeapon = Instance.new("BoolValue")
					isWeapon.Name = "IsWeapon"
					isWeapon.Parent = player.Character
					isWeapon.Value = true
				else
					isWeapon.Value = true
				end
				isweapon = isWeapon.Value
				clearHighlight(state)
				updateWeaponTip(child,state)
			end
		end)
	end
	-- 注册同步事件
	NotifyManager.RegisterClientEvent("ItemGrabbed", function(data)
		log("收到抓取事件: " .. tostring(data.objectId))

		if not data.objectId then
			log("无效的抓取事件: 缺少 objectId")
			return
		end

		local obj = ObjectTracker.findObjectById(data.objectId)

		if obj then
			log("设置抓取状态: " .. obj:GetFullName())

			syncedObjects[data.objectId] = {
				playerId = data.playerId,
				position = data.position,
				rotation = data.rotation
			}

			obj.Anchored = true
			obj.CanCollide = false

			if obj:FindFirstAncestorOfClass("Model") then
				for _, part in ipairs(obj.Parent:GetChildren()) do
					if part:IsA("BasePart") then
						part.Anchored = true
						part.CanCollide = false
					end
				end
			end

			local canGrab = obj:FindFirstChild("Grabbable")
			if canGrab then canGrab.Value = true end
		else
			log("抓取事件失败: 物体未找到")
		end
	end)
	NotifyManager.RegisterClientEvent("ItemCanGrab",function(data)
		if data.canGrab == true then
			local obj = data.character:FindFirstChild(data.toolName)
			if obj  then
				local handle = obj:FindFirstChild("Handle")
				local canGrab = handle:FindFirstChild("Grabbable")
				canGrab.Value = data.canGrab
			end
		else
			local objectId = data.objectId
			local obj = ObjectTracker.findObjectById(objectId)
			if obj then
				currentWeaponCount -= 1
				local canGrab = obj:FindFirstChild("Grabbable")
				canGrab.Value = data.canGrab
				warn(canGrab.Value)
			end
		end

	end)
	NotifyManager.RegisterClientEvent("ItemMoved", function(data)
		if not data.objectId or not data.position then
			return
		end

		local obj = ObjectTracker.findObjectById(data.objectId)

		if obj and obj:IsDescendantOf(workspace) then
			if obj:FindFirstAncestorOfClass("Model") and obj.Parent.PrimaryPart then
				obj.Parent:SetPrimaryPartCFrame(CFrame.new(data.position))
			else
				obj.Position = data.position
			end
		end
	end)

	NotifyManager.RegisterClientEvent("ItemRotated", function(data)
		log("收到旋转事件: " .. data.objectId)

		local obj = ObjectTracker.findObjectById(data.objectId)

		if obj and obj:FindFirstChild("RotationValues") then
			local rv = obj.RotationValues
			-- 强制校验旋转值范围（0-2π），避免累积误差
			rv.X.Value = data.rotation.X % (2*math.pi)
			rv.Y.Value = data.rotation.Y % (2*math.pi)
			rv.Z.Value = data.rotation.Z % (2*math.pi)
		end
	end)

	NotifyManager.RegisterClientEvent("ItemReleased", function(data)
		log("收到释放事件: " .. data.objectId)

		local obj = ObjectTracker.findObjectById(data.objectId)

		if obj and obj:IsDescendantOf(workspace) then
			log("释放物体: " .. obj:GetFullName())

			local canGrab = obj:FindFirstChild("Grabbable")
			if canGrab then canGrab.Value = false end

			-- 处理模型
			local model = obj:FindFirstAncestorOfClass("Model")
			if model and model.PrimaryPart then
				-- 重置所有子部件状态
				for _, part in ipairs(model:GetChildren()) do
					if part:IsA("BasePart") then
						part.Anchored = false
						part.CanCollide = true
					end
				end
			else
				obj.Anchored = false
				obj.CanCollide = true
			end
		else
			log("释放事件失败: 物体未找到")
		end

		syncedObjects[data.objectId] = nil
	end)

	NotifyManager.RegisterClientEvent("ItemDestroyed", function(data)
		log("收到销毁事件: " .. data.objectId)
		syncedObjects[data.objectId] = nil
	end)

	-- 处理焊接事件
	NotifyManager.RegisterClientEvent("ItemWelded", function(data)
		local objectId = data.objectId
		local targetId = data.targetId

		local obj = ObjectTracker.findObjectById(objectId)
		local target = ObjectTracker.findObjectById(targetId)

		if obj and target then
			-- 创建焊接约束
			createWeld(obj, target)

			local canGrab = obj:FindFirstChild("Grabbable")
			if canGrab then canGrab.Value = false end

			log("客户端同步焊接: " .. objectId .. " → " .. targetId)
		else
			log("客户端焊接同步失败: 对象不存在")
		end
	end)

	-- 处理解除焊接事件
	NotifyManager.RegisterClientEvent("ItemUnwelded", function(data)
		local objectId = data.objectId

		local obj = ObjectTracker.findObjectById(objectId)

		if obj then
			-- 移除焊接约束
			removeWelds(obj)

			log("客户端同步解除焊接: " .. objectId)
		else
			log("客户端解除焊接同步失败: 对象不存在")
		end
	end)

	-- 输入处理
	UIS.InputBegan:Connect(function(input, processed)
		if processed or PlayerDownedClient.IsDowned then return end

		-- 检查是否装备了武器
		if player.Character then
			for _, item in ipairs(player.Character:GetChildren()) do
				if item:IsA("Tool") then
					return
				end
			end
		end

		if input.UserInputType == Enum.UserInputType.MouseButton1 then
			if state.heldObject then
				handleRelease(state, player)
			else
				local hit, _, _ = raycastCenter(camera, MAX_HOLD_DISTANCE, {player.Character, camera})
				if hit then 
					grabObject(state, hit) 					
				end
			end
		end

		if state.heldObject and input.KeyCode == Enum.KeyCode.Z then
			state.rotAxis = (state.rotAxis + 1) % 3
			-- 更新旋转轴显示
			local axisName = "Y"
			if state.rotAxis == 0 then
				axisName = "Y"
			elseif state.rotAxis == 1 then
				axisName = "X"
			else
				axisName = "Z"
			end

			drawRotationAxis(state, axisName)
		end

		if state.heldObject and input.KeyCode == Enum.KeyCode.R then
			ensureRotationValues(state.heldObject)

			local rv = state.heldObject.RotationValues
			local delta = ROTATION_SPEED * math.pi/12  * ROTATION_DIRECTION
			local axisName = "Y" -- 默认Y轴
			if state.rotAxis == 0 then
				rv.Y.Value = rv.Y.Value + delta
				axisName = "Y"
			elseif state.rotAxis == 1 then
				rv.X.Value = rv.X.Value + delta
				axisName = "X"
			else
				rv.Z.Value = rv.Z.Value + delta
				axisName = "Z"
			end

			-- 绘制当前旋转轴
			drawRotationAxis(state, axisName)

			ProtocolManager.SendMessage("ItemRotateEvent", {
				objectId = state.heldObject.SyncId.Value,
				rotation = {
					X = rv.X.Value,
					Y = rv.Y.Value,
					Z = rv.Z.Value
				}
			})
		end

		if input.KeyCode == Enum.KeyCode.F then

			if state.heldObject then
				itemToBag(state.heldObject, state)
			else
				local hit, _, _ = raycastCenter(camera, MAX_HOLD_DISTANCE, {player.Character, camera})
				if hit then 
					local canGrab = hit:FindFirstChild("Grabbable")
					if not canGrab or canGrab.Value == true then
						warn("物品无法交互或已被抓取")
						return
					end
					itemToBag(hit, state)
				end
			end			
		end

		if input.KeyCode == Enum.KeyCode.E then
			if state.heldObject then
				itemToEquip(state.heldObject, state)
			else
				local hit, _, _ = raycastCenter(camera, MAX_HOLD_DISTANCE, {player.Character, camera})
				if hit then 
					local canGrab = hit:FindFirstChild("Grabbable")
					if not canGrab or canGrab.Value == true then
						warn("物品无法交互或已被抓取")
						return
					end
					itemToEquip(hit, state)
				end
			end			
		end
	end)

	-- 渲染步进更新
	RunService.RenderStepped:Connect(function()
		if PlayerDownedClient.IsDowned then return end
		-- 高亮逻辑
		if not state.heldObject then
			local hit, _, _ = raycastCenter(camera, MAX_HOLD_DISTANCE, {player.Character, camera})
			if hit ~= state.lastTarget then
				if isweapon then 
					clearHighlight(state)
					clearItemUI(state)
					return
				end
				clearHighlight(state)
				clearItemUI(state)
				if hit and hasTag(hit, "Grabbable") then
					local canGrab 
					local obj = nil
					local model = hit:FindFirstAncestorOfClass("Model")
					if model then
					   canGrab = model.PrimaryPart:FindFirstChild("Grabbable")
						obj = model.PrimaryPart
					else
						canGrab = hit:FindFirstChild("Grabbable")
						obj = hit
					end
					if canGrab and canGrab.Value == false then
						updateItemTips(state, obj)
						createHighlight(state, obj)
						creaeItemUI(state,obj)
						state.lastTarget = hit
					end
				else
					clearItemUI(state)
					uiTipVisable(state)
					state.lastTarget = nil
				end
			end
			if state.highlight and not hit then
				clearHighlight(state)
				clearItemUI(state)
				uiTipVisable(state)
				state.lastTarget = nil
			end
		end

		-- 更新持物
		if state.heldObject then
			local obj = state.heldObject
			local objMagnitude = obj.Size.Magnitude
			local rv = obj.RotationValues
			local cfr = camera.CFrame
			local targetPos = cfr.Position + cfr.LookVector * state.currentHoldDistance
			local rayDir = (targetPos - cfr.Position)
			local rayLength = rayDir.Magnitude
			rayDir = rayDir.Unit

			local baseIgnore = {player.Character, camera, obj}
			local ignoreList = baseIgnore

			-- 如果是模型的一部分，获取所有子部件加入忽略列表
			local model = obj:FindFirstAncestorOfClass("Model")
			if model then
				local modelParts = getModelParts(model)
				-- 合并基础忽略列表和模型部件
				for _, part in ipairs(modelParts) do
					table.insert(ignoreList, part)
				end
			end
			local params = RaycastParams.new()
			params.FilterType = Enum.RaycastFilterType.Exclude
			params.FilterDescendantsInstances = {player.Character, camera, obj}

			local hit = workspace:Raycast(cfr.Position, rayDir * rayLength, params)
			local actualPos

			if hit and hit.Instance.CanCollide then
				actualPos = hit.Position - rayDir * COLLISION_OFFSET * objMagnitude
				state.currentHoldDistance = math.min(
					(actualPos - cfr.Position).Magnitude, 
					state.currentHoldDistance
				)
			else
				state.currentHoldDistance = math.min(
					state.currentHoldDistance + 0.5,
					state.originalDistance
				)
				actualPos = targetPos
			end

			-- 向下检测射线，防止物品穿透下方物体
			local downRayParams = RaycastParams.new()
			downRayParams.FilterType = Enum.RaycastFilterType.Exclude
			downRayParams.FilterDescendantsInstances = ignoreList

			-- 物品高度的一半 + 缓冲距离
			local downRayLength 
			if obj:FindFirstAncestorOfClass("Model") and obj.Parent.PrimaryPart then
				downRayLength = obj.Size.Y *1.5 +1
			else				
				downRayLength = obj.Size.Y * 0.5 + 1
			end
			local downRayResult = workspace:Raycast(actualPos, Vector3.new(0, -downRayLength, 0), downRayParams)

			if downRayResult then
				-- 计算所需的向上偏移量（确保物品底部高于碰撞点）
				local heightOffset
				if obj:FindFirstAncestorOfClass("Model") and obj.Parent.PrimaryPart then
					heightOffset = obj.Size.Y * 2 + 0.1
				else				
					heightOffset = obj.Size.Y * 0.5 + 0.1
				end
				actualPos = Vector3.new(
					actualPos.X,
					downRayResult.Position.Y + heightOffset,
					actualPos.Z
				)
			end

			-- 应用最终位置
			if obj:FindFirstAncestorOfClass("Model") and obj.Parent.PrimaryPart then
				local baseCF = CFrame.new(actualPos)
				-- 基于本地轴的旋转：先定位，再按物体自身轴旋转
				local localRotation = CFrame.Angles(rv.X.Value, rv.Y.Value, rv.Z.Value)
				obj.Parent:SetPrimaryPartCFrame(baseCF * localRotation)
			else            
				local baseCF = CFrame.new(actualPos)
				local localRotation = CFrame.Angles(rv.X.Value, rv.Y.Value, rv.Z.Value)
				obj.CFrame = baseCF * localRotation
			end

			-- 检测目标并更新UI提示
			local ignore = { player.Character, obj }
			local hitRange = obj:FindFirstAncestorOfClass("Model") and 
				DEFAULT_HOLD_DISTANCE * MODELWELDMULTIPLY  or obj.Size.Magnitude * BASEPARTWELDMULTIPLY
			local target, _, _ = detectReleaseTarget(camera, hitRange, ignore, obj)

			-- 确定目标类型（如果有）
			local targetType = nil
			if target then
				if hasTag(target, "FuelTank") then
					targetType = "FuelTank"
				elseif hasTag(target, "CargoFloor") then
					targetType = "CargoFloor"
				elseif hasTag(target, "Shop") then
					targetType = "Shop"
				elseif hasTag(target, "Sell") then
					targetType = "Sell"
				end
			end

			-- 更新物品提示UI
			updateItemTips(state, obj, targetType)
			-- 更新旋转轴位置
			if next(rotationAxes) ~= nil then
				local obj = state.heldObject
				local objModel = obj:FindFirstAncestorOfClass("Model")
				local baseCFrame = obj.CFrame

				if objModel and objModel.PrimaryPart then
					baseCFrame = objModel.PrimaryPart.CFrame
				end

				for axisName, axis in pairs(rotationAxes) do
					if axis and axis:IsDescendantOf(workspace) then
						if axisName == "X" then
							axis.CFrame = baseCFrame * CFrame.new(AXIS_LENGTH/2, 0, 0)
						elseif axisName == "Y" then
							axis.CFrame = baseCFrame * CFrame.new(0, AXIS_LENGTH/2, 0)
						elseif axisName == "Z" then
							axis.CFrame = baseCFrame * CFrame.new(0, 0, AXIS_LENGTH/2)
						end
					end
				end
			end
		end
	end)

	print("【客户端】ItemInteractionSystem loaded")
end

-- 服务器端初始化
function ItemInteractionSystem.initServer()
	Players.PlayerAdded:Connect(initPlayerState)
	Players.PlayerRemoving:Connect(function(p) playerStates[p] = nil end)
	ObjectTracker.startCleanup() -- 启动ObjectTracker清理任务
end

return ItemInteractionSystem
