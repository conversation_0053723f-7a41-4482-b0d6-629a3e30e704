--!strict
-- 传送测试管理器
-- 用于测试职业选择和数据传输功能

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TeleportManager = require(ReplicatedStorage.Scripts.Server.Manager.TeleportManager)
local ProfessionManager = require(ReplicatedStorage.Scripts.Server.Manager.ProfessionManager)
local OccupationConfig = require(ReplicatedStorage.Scripts.Config.OccupationConfig)
local OccupationDataParser = require(ReplicatedStorage.Scripts.Server.Utils.OccupationDataParser)

local TeleportTestManager = {}

-- 测试数据
local testProfessions = {
    {id = 4001, profession = "战士", price = 0},
    {id = 4002, profession = "法师", price = 100}
}

-- 为玩家设置测试职业
-- @param player: Player - 玩家对象
-- @param professionId: number - 职业ID
function TeleportTestManager.setTestProfession(player, professionId)
    local userId = player.UserId
    
    -- 查找职业配置
    local profession = nil
    for _, testProf in ipairs(testProfessions) do
        if testProf.id == professionId then
            profession = testProf.profession
            break
        end
    end
    
    if not profession then
        warn("TeleportTestManager: 未找到职业ID " .. professionId)
        return false
    end
    
    -- 直接设置职业数据（跳过购买验证）
    ProfessionManager.playerProfession[userId] = profession
    ProfessionManager.playerProfessionState[userId] = professionId
    
    print("TeleportTestManager: 为玩家 " .. player.Name .. " 设置职业: " .. profession .. " (ID:" .. professionId .. ")")
    return true
end

-- 测试物品数据解析
function TeleportTestManager.testItemParsing()
    print("=== 测试物品数据解析 ===")
    
    for _, config in ipairs(OccupationConfig) do
        print("职业ID: " .. config.Id)
        if config.CarryingItem then
            print("  原始数据: " .. config.CarryingItem)
            local items = OccupationDataParser.parseCarryingItems(config.CarryingItem)
            print("  解析结果:")
            for i, item in ipairs(items) do
                print(string.format("    [%d] ID:%d, 数量:%d, 类型:%d", 
                    i, item.id, item.quantity, item.itemType))
            end
        else
            print("  无携带物品数据")
        end
        print("")
    end
end

-- 测试单个玩家的数据生成
-- @param player: Player - 玩家对象
function TeleportTestManager.testPlayerDataGeneration(player)
    print("=== 测试玩家数据生成: " .. player.Name .. " ===")
    
    local userId = player.UserId
    local profession = ProfessionManager.playerProfession[userId]
    local professionId = ProfessionManager.playerProfessionState[userId]
    
    print("玩家职业: " .. (profession or "无"))
    print("职业ID: " .. (professionId or "无"))
    
    if professionId then
        local items = OccupationDataParser.getItemsByOccupationId(professionId, OccupationConfig)
        print("物品数据:")
        OccupationDataParser.debugPrintItems(items, player.Name)
    else
        print("无职业数据，无法生成物品")
    end
    
    print("========================")
end

-- 测试传送数据格式
-- @param players: table - 玩家数组
function TeleportTestManager.testTeleportDataFormat(players)
    print("=== 测试传送数据格式 ===")
    
    if #players == 0 then
        warn("没有玩家可测试")
        return
    end
    
    -- 模拟 TeleportManager 的数据生成逻辑
    local playersData = {}
    
    for _, player in ipairs(players) do
        local userId = player.UserId
        local playerProfessionId = ProfessionManager.playerProfessionState[userId]
        
        local items = {}
        if playerProfessionId then
            items = OccupationDataParser.getItemsByOccupationId(playerProfessionId, OccupationConfig)
        end
        
        playersData[tostring(userId)] = {
            items = items,
            profession = ProfessionManager.playerProfession[userId],
            professionId = playerProfessionId,
            playerName = player.Name
        }
    end
    
    -- 打印生成的数据结构
    print("生成的传送数据结构:")
    print("playersData = {")
    for userId, data in pairs(playersData) do
        print("  [\"" .. userId .. "\"] = {")
        print("    playerName = \"" .. data.playerName .. "\",")
        print("    profession = \"" .. (data.profession or "nil") .. "\",")
        print("    professionId = " .. (data.professionId or "nil") .. ",")
        print("    items = {")
        for i, item in ipairs(data.items) do
            print(string.format("      [%d] = {id = %d, quantity = %d, itemType = %d},", 
                i, item.id, item.quantity, item.itemType))
        end
        print("    }")
        print("  },")
    end
    print("}")
    
    print("========================")
end

-- 运行完整测试
function TeleportTestManager.runFullTest()
    print("🧪 开始完整传送测试")
    
    -- 1. 测试物品解析
    TeleportTestManager.testItemParsing()
    
    -- 2. 获取在线玩家
    local players = Players:GetPlayers()
    if #players == 0 then
        warn("没有在线玩家，无法进行测试")
        return
    end
    
    -- 3. 为玩家设置测试职业
    for i, player in ipairs(players) do
        local professionId = testProfessions[(i - 1) % #testProfessions + 1].id
        TeleportTestManager.setTestProfession(player, professionId)
    end
    
    -- 4. 测试每个玩家的数据生成
    for _, player in ipairs(players) do
        TeleportTestManager.testPlayerDataGeneration(player)
    end
    
    -- 5. 测试传送数据格式
    TeleportTestManager.testTeleportDataFormat(players)
    
    print("🧪 完整传送测试完成")
end

-- 模拟传送（不实际传送，只生成和打印数据）
function TeleportTestManager.simulateTeleport()
    print("🚀 模拟传送测试")
    
    local players = Players:GetPlayers()
    if #players == 0 then
        warn("没有在线玩家，无法模拟传送")
        return
    end
    
    -- 为每个玩家设置不同的职业
    for i, player in ipairs(players) do
        local professionId = testProfessions[(i - 1) % #testProfessions + 1].id
        TeleportTestManager.setTestProfession(player, professionId)
    end
    
    print("已为 " .. #players .. " 个玩家设置职业，准备生成传送数据...")
    
    -- 调用 TeleportManager 的数据生成逻辑（但不实际传送）
    TeleportTestManager.testTeleportDataFormat(players)
    
    print("🚀 模拟传送测试完成")
end

-- 清理测试数据
function TeleportTestManager.cleanupTestData()
    print("🧹 清理测试数据")
    
    for _, player in ipairs(Players:GetPlayers()) do
        local userId = player.UserId
        ProfessionManager.playerProfession[userId] = nil
        ProfessionManager.playerProfessionState[userId] = nil
        print("已清理玩家 " .. player.Name .. " 的职业数据")
    end
    
    print("🧹 测试数据清理完成")
end

-- 设置测试命令
function TeleportTestManager.setupTestCommands()
    print("设置传送测试命令...")
    
    for _, player in ipairs(Players:GetPlayers()) do
        TeleportTestManager.setupPlayerTestCommands(player)
    end
    
    Players.PlayerAdded:Connect(function(player)
        TeleportTestManager.setupPlayerTestCommands(player)
    end)
    
    print("传送测试命令设置完成")
end

-- 为单个玩家设置测试命令
function TeleportTestManager.setupPlayerTestCommands(player)
    player.Chatted:Connect(function(message)
        if message == "/test_teleport" then
            TeleportTestManager.runFullTest()
        elseif message == "/simulate_teleport" then
            TeleportTestManager.simulateTeleport()
        elseif message == "/cleanup_test" then
            TeleportTestManager.cleanupTestData()
        elseif message == "/set_warrior" then
            TeleportTestManager.setTestProfession(player, 4001)
            print("已为 " .. player.Name .. " 设置战士职业")
        elseif message == "/set_mage" then
            TeleportTestManager.setTestProfession(player, 4002)
            print("已为 " .. player.Name .. " 设置法师职业")
        elseif message == "/test_help" then
            print("传送测试命令:")
            print("  /test_teleport - 运行完整测试")
            print("  /simulate_teleport - 模拟传送")
            print("  /cleanup_test - 清理测试数据")
            print("  /set_warrior - 设置战士职业")
            print("  /set_mage - 设置法师职业")
        end
    end)
end

return TeleportTestManager
