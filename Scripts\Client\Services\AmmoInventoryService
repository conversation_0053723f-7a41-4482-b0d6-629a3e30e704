local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local RemoteWeaponConfig = require(ReplicatedStorage.Scripts.Config.RemoteWeaponConfig)
local ProtocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local AmmoInventoryService = {}

-- 弹药库存数据
AmmoInventoryService.AmmoInventory = {}
AmmoInventoryService.AmmoUI = nil
AmmoInventoryService.IsUIOpen = false

-- 初始化弹药库
function AmmoInventoryService:Initialize()
	local player = Players.LocalPlayer
	if not player then return end

	print("初始化弹药库系统")

	-- 初始化弹药库存数据
	self:InitializeAmmoData()

	-- 创建UI
	self:CreateAmmoInventoryUI()

	-- 注册服务端通知事件
	NotifyService.RegisterClientEvent("AmmoInventoryUpdate", function(data)
		if data.ammoId and data.amount then
			self:UpdateAmmoAmount(data.ammoId, data.amount)
		end
	end)

	-- 注册服务端完整数据更新
	NotifyService.RegisterClientEvent("AmmoInventorySync", function(data)
		if data.inventory then
			self.AmmoInventory = data.inventory
			self:RefreshAmmoInventoryUI()
		end
	end)

	print("弹药库系统初始化完成")
end

-- 初始化弹药数据
function AmmoInventoryService:InitializeAmmoData()
	-- 从RemoteWeaponConfig中提取所有弹药类型
	for _, config in ipairs(RemoteWeaponConfig) do
		-- 使用BallisticId作为弹药ID
		if config.BallisticId and not self.AmmoInventory[config.BallisticId] then
			self.AmmoInventory[config.BallisticId] = {
				id = config.BallisticId,
				name = config.BallisticModel or "未知弹药",
				amount = 0, -- 初始数量为0
				modelName = config.BallisticModel,
				weaponId = config.Id
			}
		end
	end

	-- 初始化时所有弹药数量为0，等待服务器同步
	for id, _ in pairs(self.AmmoInventory) do
		self.AmmoInventory[id].amount = 0
	end

	print("客户端弹药库初始化完成，所有弹药数量设为0，等待服务器同步")
end

-- 更新弹药数量
function AmmoInventoryService:UpdateAmmoAmount(ammoId, amount)
	if self.AmmoInventory[ammoId] then
		self.AmmoInventory[ammoId].amount = amount

		-- 如果UI打开，刷新显示
		if self.IsUIOpen and self.AmmoUI then
			self:RefreshAmmoInventoryUI()
		end

		-- 如果当前装备的武器使用这种弹药，更新武器UI
		local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
		if WeaponClient.IsWeaponEquipped and WeaponClient.RemoteData and WeaponClient.RemoteData.BallisticId == ammoId then
			WeaponClient:UpdateAmmoUI()
		end

		print("更新弹药库存: ID=" .. ammoId .. ", 数量=" .. amount)
	end
end

-- 增加弹药数量
function AmmoInventoryService:AddAmmo(ammoId, amount)
	if self.AmmoInventory[ammoId] then
		self.AmmoInventory[ammoId].amount = self.AmmoInventory[ammoId].amount + amount

		-- 通知服务器更新弹药数量
		ProtocolManager.SendMessage("UpdateAmmoInventory", {
			ammoId = ammoId,
			amount = self.AmmoInventory[ammoId].amount
		})

		-- 如果UI打开，刷新显示
		if self.IsUIOpen and self.AmmoUI then
			self:RefreshAmmoInventoryUI()
		end

		print("增加弹药: ID=" .. ammoId .. ", 增加=" .. amount .. ", 当前=" .. self.AmmoInventory[ammoId].amount)
		return true
	end
	return false
end

-- 减少弹药数量
function AmmoInventoryService:RemoveAmmo(ammoId, amount)
	if self.AmmoInventory[ammoId] and self.AmmoInventory[ammoId].amount >= amount then
		self.AmmoInventory[ammoId].amount = self.AmmoInventory[ammoId].amount - amount

		-- 通知服务器更新弹药数量
		ProtocolManager.SendMessage("UpdateAmmoInventory", {
			ammoId = ammoId,
			amount = self.AmmoInventory[ammoId].amount
		})

		-- 如果UI打开，刷新显示
		if self.IsUIOpen and self.AmmoUI then
			self:RefreshAmmoInventoryUI()
		end

		print("减少弹药: ID=" .. ammoId .. ", 减少=" .. amount .. ", 当前=" .. self.AmmoInventory[ammoId].amount)
		return true
	end
	return false
end

-- 获取弹药数量
function AmmoInventoryService:GetAmmoAmount(ammoId)
	if self.AmmoInventory[ammoId] then
		return self.AmmoInventory[ammoId].amount
	end
	return 0
end

-- 创建弹药库UI
function AmmoInventoryService:CreateAmmoInventoryUI()
	local player = Players.LocalPlayer
	if not player then return end

	-- 检查是否已存在UI
	if self.AmmoUI and self.AmmoUI.Parent then
		return self.AmmoUI
	end

	-- 获取或创建PlayerGui
	local playerGui = player:FindFirstChild("PlayerGui")
	if not playerGui then
		playerGui = Instance.new("ScreenGui")
		playerGui.Name = "PlayerGui"
		playerGui.Parent = player
	end

	-- 创建弹药库UI的ScreenGui
	local ammoScreenGui = Instance.new("ScreenGui")
	ammoScreenGui.Name = "AmmoInventoryUI"
	ammoScreenGui.ResetOnSpawn = false
	ammoScreenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
	ammoScreenGui.Enabled = false -- 默认隐藏
	ammoScreenGui.Parent = playerGui

	-- 创建主背景框
	local mainFrame = Instance.new("Frame")
	mainFrame.Name = "MainFrame"
	mainFrame.Size = UDim2.new(0, 400, 0, 300)
	mainFrame.Position = UDim2.new(0.5, -200, 0.5, -150) -- 居中
	mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
	mainFrame.BorderSizePixel = 0
	mainFrame.Parent = ammoScreenGui

	-- 添加圆角效果
	local uiCorner = Instance.new("UICorner")
	uiCorner.CornerRadius = UDim.new(0, 10)
	uiCorner.Parent = mainFrame

	-- 创建标题
	local titleLabel = Instance.new("TextLabel")
	titleLabel.Name = "TitleLabel"
	titleLabel.Size = UDim2.new(1, 0, 0, 40)
	titleLabel.Position = UDim2.new(0, 0, 0, 0)
	titleLabel.BackgroundTransparency = 1
	titleLabel.Font = Enum.Font.SourceSansBold
	titleLabel.TextSize = 24
	titleLabel.TextColor3 = Color3.new(1, 1, 1)
	titleLabel.Text = "弹药库"
	titleLabel.Parent = mainFrame

	-- 创建关闭按钮
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 30, 0, 30)
	closeButton.Position = UDim2.new(1, -40, 0, 10)
	closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.TextSize = 18
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.Text = "X"
	closeButton.Parent = mainFrame

	-- 添加圆角效果
	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 5)
	closeCorner.Parent = closeButton

	-- 创建弹药列表滚动框架
	local scrollFrame = Instance.new("ScrollingFrame")
	scrollFrame.Name = "AmmoListFrame"
	scrollFrame.Size = UDim2.new(1, -20, 1, -60)
	scrollFrame.Position = UDim2.new(0, 10, 0, 50)
	scrollFrame.BackgroundTransparency = 1
	scrollFrame.ScrollBarThickness = 6
	scrollFrame.ScrollingDirection = Enum.ScrollingDirection.Y
	scrollFrame.CanvasSize = UDim2.new(0, 0, 0, 0) -- 会在填充内容时动态调整
	scrollFrame.Parent = mainFrame

	-- 设置列表布局
	local listLayout = Instance.new("UIListLayout")
	listLayout.Padding = UDim.new(0, 5)
	listLayout.SortOrder = Enum.SortOrder.LayoutOrder
	listLayout.Parent = scrollFrame

	-- 关闭按钮点击事件
	closeButton.MouseButton1Click:Connect(function()
		self:ToggleAmmoInventoryUI()
	end)

	self.AmmoUI = ammoScreenGui

	-- 填充弹药数据
	self:RefreshAmmoInventoryUI()

	return ammoScreenGui
end

-- 刷新弹药库UI显示
function AmmoInventoryService:RefreshAmmoInventoryUI()
	if not self.AmmoUI then
		self:CreateAmmoInventoryUI()
	end

	local scrollFrame = self.AmmoUI.MainFrame.AmmoListFrame

	-- 清除现有项目
	for _, child in pairs(scrollFrame:GetChildren()) do
		if child:IsA("Frame") then
			child:Destroy()
		end
	end

	-- 添加弹药项目
	local yOffset = 0
	local index = 0

	for _, ammoData in pairs(self.AmmoInventory) do
		-- 创建弹药项目框架
		local ammoItemFrame = Instance.new("Frame")
		ammoItemFrame.Name = "AmmoItem_" .. ammoData.id
		ammoItemFrame.Size = UDim2.new(1, -10, 0, 50)
		ammoItemFrame.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
		ammoItemFrame.BorderSizePixel = 0
		ammoItemFrame.LayoutOrder = index
		ammoItemFrame.Parent = scrollFrame

		-- 添加圆角效果
		local itemCorner = Instance.new("UICorner")
		itemCorner.CornerRadius = UDim.new(0, 5)
		itemCorner.Parent = ammoItemFrame

		-- 创建弹药ID标签
		local idLabel = Instance.new("TextLabel")
		idLabel.Name = "IdLabel"
		idLabel.Size = UDim2.new(0.2, 0, 1, 0)
		idLabel.Position = UDim2.new(0, 10, 0, 0)
		idLabel.BackgroundTransparency = 1
		idLabel.Font = Enum.Font.SourceSans
		idLabel.TextSize = 16
		idLabel.TextColor3 = Color3.new(1, 1, 1)
		idLabel.TextXAlignment = Enum.TextXAlignment.Left
		idLabel.Text = "ID: " .. ammoData.id
		idLabel.Parent = ammoItemFrame

		-- 创建弹药名称标签
		local nameLabel = Instance.new("TextLabel")
		nameLabel.Name = "NameLabel"
		nameLabel.Size = UDim2.new(0.4, 0, 1, 0)
		nameLabel.Position = UDim2.new(0.2, 10, 0, 0)
		nameLabel.BackgroundTransparency = 1
		nameLabel.Font = Enum.Font.SourceSans
		nameLabel.TextSize = 16
		nameLabel.TextColor3 = Color3.new(1, 1, 1)
		nameLabel.TextXAlignment = Enum.TextXAlignment.Left
		nameLabel.Text = "名称: " .. ammoData.name
		nameLabel.Parent = ammoItemFrame

		-- 创建弹药数量标签
		local amountLabel = Instance.new("TextLabel")
		amountLabel.Name = "AmountLabel"
		amountLabel.Size = UDim2.new(0.2, 0, 1, 0)
		amountLabel.Position = UDim2.new(0.6, 10, 0, 0)
		amountLabel.BackgroundTransparency = 1
		amountLabel.Font = Enum.Font.SourceSansBold
		amountLabel.TextSize = 18
		amountLabel.TextColor3 = Color3.new(1, 1, 0)
		amountLabel.TextXAlignment = Enum.TextXAlignment.Left
		amountLabel.Text = "数量: " .. ammoData.amount
		amountLabel.Parent = ammoItemFrame

		-- 更新y偏移
		yOffset = yOffset + 55 -- 50高度 + 5间距
		index = index + 1
	end

	-- 更新滚动框架的画布大小
	scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yOffset)
end

-- 切换弹药库UI显示状态
function AmmoInventoryService:ToggleAmmoInventoryUI()
	if not self.AmmoUI then
		self:CreateAmmoInventoryUI()
	end

	self.IsUIOpen = not self.IsUIOpen
	self.AmmoUI.Enabled = self.IsUIOpen

	-- 如果打开UI，刷新数据并请求服务器同步最新数据
	if self.IsUIOpen then
		-- 请求服务器同步最新弹药数据
		ProtocolManager.SendMessage("RequestAmmoInventorySync", {})

		-- 使用当前数据先刷新UI
		self:RefreshAmmoInventoryUI()
	end

	print("弹药库UI显示状态: " .. (self.IsUIOpen and "显示" or "隐藏"))
end

return AmmoInventoryService 