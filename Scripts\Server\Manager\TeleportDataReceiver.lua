--!strict
-- 传送数据接收管理器
-- 用于处理从初始场景传送过来的玩家数据

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TeleportService = game:GetService("TeleportService")

local TeleportDataReceiver = {}

-- 存储玩家的传送数据
TeleportDataReceiver.playerData = {}

-- 解析并存储玩家的传送数据
-- @param player: Player - 玩家对象
-- @param teleportData: table - 传送数据
local function processPlayerTeleportData(player, teleportData)
    local userId = tostring(player.UserId)
    
    if not teleportData then
        warn("TeleportDataReceiver: 玩家 " .. player.Name .. " 没有传送数据")
        return
    end
    
    -- 检查是否有个性化数据
    if teleportData.playersData and teleportData.playersData[userId] then
        local playerData = teleportData.playersData[userId]
        
        -- 存储玩家数据
        TeleportDataReceiver.playerData[userId] = {
            items = playerData.items or {},
            profession = playerData.profession,
            professionId = playerData.professionId,
            playerName = playerData.playerName or player.Name,
            timestamp = teleportData.timestamp or os.time()
        }
        
        print("TeleportDataReceiver: 成功接收玩家 " .. player.Name .. " 的个性化数据")
        print("  - 职业: " .. (playerData.profession or "无"))
        print("  - 职业ID: " .. (playerData.professionId or "无"))
        print("  - 物品数量: " .. #(playerData.items or {}))
        
        -- 调试输出物品详情
        if playerData.items and #playerData.items > 0 then
            print("  - 物品详情:")
            for i, item in ipairs(playerData.items) do
                print(string.format("    [%d] ID:%d, 数量:%d, 类型:%d", 
                    i, item.id, item.quantity, item.itemType))
            end
        end
        
    else
        -- 尝试使用旧格式的全局数据（向后兼容）
        warn("TeleportDataReceiver: 玩家 " .. player.Name .. " 使用旧格式数据或无个性化数据")
        
        -- 从全局职业数据中获取
        local profession = nil
        local professionId = nil
        
        if teleportData.professionPlayer and teleportData.professionPlayer[userId] then
            profession = teleportData.professionPlayer[userId]
        end
        
        if teleportData.professionState and teleportData.professionState[userId] then
            professionId = teleportData.professionState[userId]
        end
        
        TeleportDataReceiver.playerData[userId] = {
            items = teleportData.items or {}, -- 使用全局物品列表
            profession = profession,
            professionId = professionId,
            playerName = player.Name,
            timestamp = teleportData.timestamp or os.time()
        }
    end
end

-- 获取玩家的传送数据
-- @param player: Player - 玩家对象
-- @return table - 玩家数据，如果没有则返回nil
function TeleportDataReceiver.getPlayerData(player)
    local userId = tostring(player.UserId)
    return TeleportDataReceiver.playerData[userId]
end

-- 获取玩家的物品数据
-- @param player: Player - 玩家对象
-- @return table - 物品数组
function TeleportDataReceiver.getPlayerItems(player)
    local playerData = TeleportDataReceiver.getPlayerData(player)
    return playerData and playerData.items or {}
end

-- 获取玩家的职业信息
-- @param player: Player - 玩家对象
-- @return string, number - 职业名称和职业ID
function TeleportDataReceiver.getPlayerProfession(player)
    local playerData = TeleportDataReceiver.getPlayerData(player)
    if playerData then
        return playerData.profession, playerData.professionId
    end
    return nil, nil
end

-- 清理玩家数据（当玩家离开时）
-- @param player: Player - 玩家对象
function TeleportDataReceiver.cleanupPlayerData(player)
    local userId = tostring(player.UserId)
    TeleportDataReceiver.playerData[userId] = nil
    print("TeleportDataReceiver: 清理玩家 " .. player.Name .. " 的数据")
end

-- 验证玩家数据完整性
-- @param player: Player - 玩家对象
-- @return boolean - 数据是否完整
function TeleportDataReceiver.validatePlayerData(player)
    local playerData = TeleportDataReceiver.getPlayerData(player)
    
    if not playerData then
        warn("TeleportDataReceiver: 玩家 " .. player.Name .. " 没有传送数据")
        return false
    end
    
    if not playerData.profession or not playerData.professionId then
        warn("TeleportDataReceiver: 玩家 " .. player.Name .. " 缺少职业信息")
        return false
    end
    
    if not playerData.items or type(playerData.items) ~= "table" then
        warn("TeleportDataReceiver: 玩家 " .. player.Name .. " 物品数据格式错误")
        return false
    end
    
    return true
end

-- 初始化传送数据接收器
function TeleportDataReceiver.init()
    -- 监听玩家加入事件
    Players.PlayerAdded:Connect(function(player)
        -- 获取传送数据
        local teleportData = TeleportService:GetTeleportData()
        
        if teleportData then
            print("TeleportDataReceiver: 检测到传送数据，处理玩家 " .. player.Name)
            processPlayerTeleportData(player, teleportData)
        else
            print("TeleportDataReceiver: 玩家 " .. player.Name .. " 直接加入（无传送数据）")
        end
    end)
    
    -- 监听玩家离开事件
    Players.PlayerRemoving:Connect(function(player)
        TeleportDataReceiver.cleanupPlayerData(player)
    end)
    
    print("TeleportDataReceiver: 初始化完成")
end

-- 调试函数：打印所有玩家数据
function TeleportDataReceiver.debugPrintAllData()
    print("=== TeleportDataReceiver 调试信息 ===")
    for userId, data in pairs(TeleportDataReceiver.playerData) do
        print("玩家ID: " .. userId)
        print("  - 姓名: " .. data.playerName)
        print("  - 职业: " .. (data.profession or "无"))
        print("  - 职业ID: " .. (data.professionId or "无"))
        print("  - 物品数量: " .. #data.items)
        print("  - 时间戳: " .. data.timestamp)
    end
    print("=====================================")
end

return TeleportDataReceiver
