--!strict
export type SkillConfig = {
  Id: number,
  Chinese: string,
  Name: string,
  AttackAction: string,
  Cooldown: number,
  AttackSoundEffects: string,
  HitDamage: number,
  HitSoundEffects: string,
  AttackSpecialEffects: string
}

local SkillConfig: {SkillConfig} = {
  {Id=10001, Chinese="第一式", Name="SwordSlice_1", AttackAction="rbxassetid://76349097893869", Cooldown=0.2, AttackSoundEffects="rbxassetid://2227417262", HitDamage=1.5, HitSoundEffects="rbxassetid://3744368365", AttackSpecialEffects="Charging"},
  {Id=10002, Chinese="第二式", Name="SwordSlice_2", AttackAction="rbxassetid://82752679729445", Cooldown=0.5, AttackSoundEffects="rbxassetid://4471648128", HitDamage=2, HitSoundEffects="rbxassetid://3744368365", AttackSpecialEffects="Effect"},
  {Id=10003, Chinese="第三式", Name="SwordSlice_3", AttackAction="rbxassetid://70821259122360", Cooldown=1, AttackSoundEffects="rbxassetid://4122437525", HitDamage=2.5, HitSoundEffects="rbxassetid://3744368365", AttackSpecialEffects="Whirl"},
  {Id=10004, Chinese="第四式", Name="SwordSlice_4", AttackAction="rbxassetid://70386566804926", Cooldown=0.2, AttackSoundEffects="rbxassetid://82291770077841", HitDamage=1.2, HitSoundEffects="rbxassetid://117561646771603", AttackSpecialEffects="WhirlLowPoly"},
  {Id=10005, Chinese="第五式", Name="SwordSlice_5", AttackAction="rbxassetid://125338521943168", Cooldown=0.5, AttackSoundEffects="rbxassetid://4815023971", HitDamage=1.5, HitSoundEffects="rbxassetid://117561646771603", AttackSpecialEffects="slash"},
  {Id=10006, Chinese="第六式", Name="SwordSlice_6", AttackAction="rbxassetid://70814853655011", Cooldown=1, AttackSoundEffects="rbxassetid://3744368365", HitDamage=2, HitSoundEffects="rbxassetid://117561646771603", AttackSpecialEffects="Shockwave"}
}

return SkillConfig
