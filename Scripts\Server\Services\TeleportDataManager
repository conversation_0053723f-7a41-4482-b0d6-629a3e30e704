--!strict
-- 传输数据管理器
-- 负责处理场景间传输的数据，并与新手盒子系统集成

local TeleportService = game:GetService("TeleportService")
local Players = game:GetService("Players")

-- 类型定义
type Player = Player
type TeleportData = {
	-- 旧格式兼容
	job: string?,
	str: string?,
	items: {{
		id: number,
		quantity: number,
		itemType: number
	}}?,
	-- 新格式支持
	playersData: {[string]: {
		items: {{
			id: number,
			quantity: number,
			itemType: number
		}},
		profession: string?,
		professionId: number?,
		playerName: string?
	}}?,
	professionPlayer: {[string]: string}?,
	professionState: {[string]: number}?,
	sourcePlace: number?,
	timestamp: number?
}
type DataStatus = "waiting" | "received" | "timeout" | "no_data" | "unknown"

local TeleportDataManager = {}

-- 存储玩家的传输数据
local playerTeleportData: {[number]: TeleportData} = {}

-- 存储玩家数据接收状态
local playerDataStatus: {[number]: DataStatus} = {}

-- 数据就绪通知事件
local dataReadyEvent = Instance.new("BindableEvent")

-- 验证物品数组格式
local function validateItemsArray(items)
	if not items or type(items) ~= "table" then
		return false, "物品数组无效"
	end

	for i, item in ipairs(items) do
		if not item.id or type(item.id) ~= "number" then
			return false, "物品 " .. i .. " 缺少有效的id字段"
		end
		if not item.quantity or type(item.quantity) ~= "number" or item.quantity <= 0 then
			return false, "物品 " .. i .. " 缺少有效的quantity字段"
		end
		if not item.itemType or type(item.itemType) ~= "number" then
			return false, "物品 " .. i .. " 缺少有效的itemType字段"
		end
	end

	return true, "物品数组验证通过"
end

-- 数据验证函数（支持新旧格式）
local function validateTeleportData(data)
	if not data then
		return false, "数据为空"
	end

	-- 检查是否为新格式（有playersData字段）
	if data.playersData then
		-- 验证新格式
		if type(data.playersData) ~= "table" then
			return false, "playersData字段格式错误"
		end

		for userId, playerData in pairs(data.playersData) do
			if type(playerData) ~= "table" then
				return false, "玩家数据格式错误: " .. userId
			end

			if playerData.items then
				local isValid, errorMsg = validateItemsArray(playerData.items)
				if not isValid then
					return false, "玩家 " .. userId .. " 的" .. errorMsg
				end
			end
		end

		return true, "新格式数据验证通过"
	else
		-- 验证旧格式
		if data.items then
			local isValid, errorMsg = validateItemsArray(data.items)
			if not isValid then
				return false, errorMsg
			end
		end

		return true, "旧格式数据验证通过"
	end
end

-- 获取玩家的传输数据（从内存缓存中获取，数据由客户端提供）
function TeleportDataManager:GetPlayerTeleportData(player: Player): TeleportData?
	if not player then
		warn("GetPlayerTeleportData: player参数为空")
		return nil
	end

	-- 从内存中获取数据（数据由客户端通过RemoteEvent发送）
	local cachedData = playerTeleportData[player.UserId]
	if cachedData then
		print("从缓存获取玩家传输数据: " .. player.Name)
		return cachedData
	end

	print("玩家 " .. player.Name .. " 没有传输数据（等待客户端发送）")
	return nil
end

-- 接收客户端发送的传输数据
function TeleportDataManager:ReceiveTeleportDataFromClient(player: Player, teleportData: TeleportData?): boolean
	if not player then
		warn("ReceiveTeleportDataFromClient: player参数为空")
		return false
	end

	if not teleportData then
		-- 标记为没有传输数据
		playerDataStatus[player.UserId] = "no_data"
		-- 清理可能存在的旧数据
		playerTeleportData[player.UserId] = nil

		-- 触发数据就绪事件
		dataReadyEvent:Fire(player, "no_data")
		print("玩家 " .. player.Name .. " 确认没有传输数据")
		return true
	end

	-- 验证数据格式
	local isValid, errorMsg = validateTeleportData(teleportData)
	if not isValid then
		-- 标记为数据无效，当作没有数据处理
		playerDataStatus[player.UserId] = "no_data"
		dataReadyEvent:Fire(player, "invalid_data")
		warn("玩家 " .. player.Name .. " 的传输数据无效: " .. errorMsg)
		return false
	end

	-- 存储数据到内存
	playerTeleportData[player.UserId] = teleportData
	-- 标记为已接收数据
	playerDataStatus[player.UserId] = "received"

	-- 打印详细信息用于调试
	print("传输数据详情:")
	print("  职业: " .. (teleportData.job or "未知"))
	print("  描述: " .. (teleportData.str or "未知"))
	print("  物品列表:")
	for i, item in ipairs(teleportData.items) do
		print(string.format("    %d. ID:%d, 数量:%d, 类型:%d", i, item.id, item.quantity, item.itemType))
	end

	-- 触发数据就绪事件
	dataReadyEvent:Fire(player, "received")
	print("玩家 " .. player.Name .. " 传输数据已就绪")

	return true
end

-- 获取玩家的物品数据（支持新旧格式）
function TeleportDataManager:GetPlayerItems(player: Player)
	local teleportData = self:GetPlayerTeleportData(player)

	if not teleportData then
		return nil
	end

	-- 优先使用新格式的个性化数据
	if teleportData.playersData then
		local userId = tostring(player.UserId)
		local playerData = teleportData.playersData[userId]
		if playerData and playerData.items then
			print("使用新格式获取玩家 " .. player.Name .. " 的物品数据")
			return playerData.items
		end
	end

	-- 回退到旧格式
	if teleportData.items then
		print("使用旧格式获取玩家 " .. player.Name .. " 的物品数据")
		return teleportData.items
	end

	return nil
end

-- 检查玩家是否有传输数据
function TeleportDataManager:HasPlayerTeleportData(player: Player): boolean
	local teleportData = self:GetPlayerTeleportData(player)
	return teleportData ~= nil
end

-- 获取玩家的职业信息（支持新旧格式）
function TeleportDataManager:GetPlayerJob(player: Player): (string?, string?)
	local teleportData = self:GetPlayerTeleportData(player)

	if not teleportData then
		return nil, nil
	end

	-- 优先使用新格式的个性化数据
	if teleportData.playersData then
		local userId = tostring(player.UserId)
		local playerData = teleportData.playersData[userId]
		if playerData then
			print("使用新格式获取玩家 " .. player.Name .. " 的职业信息")
			return playerData.profession, tostring(playerData.professionId or "")
		end
	end

	-- 回退到全局职业数据
	if teleportData.professionPlayer and teleportData.professionState then
		local userId = tostring(player.UserId)
		local profession = teleportData.professionPlayer[userId]
		local professionId = teleportData.professionState[userId]
		if profession then
			print("使用全局格式获取玩家 " .. player.Name .. " 的职业信息")
			return profession, tostring(professionId or "")
		end
	end

	-- 最后回退到旧格式
	if teleportData.job then
		print("使用旧格式获取玩家 " .. player.Name .. " 的职业信息")
		return teleportData.job, teleportData.str
	end

	return nil, nil
end

-- 清理玩家数据
function TeleportDataManager:CleanupPlayerData(player: Player)
	if not player then return end

	playerTeleportData[player.UserId] = nil
	playerDataStatus[player.UserId] = nil
	print("已清理玩家传输数据: " .. player.Name)
end

-- 初始化管理器
function TeleportDataManager:Initialize()
	print("TeleportDataManager 开始初始化")

	-- 创建或获取RemoteEvent
	local ReplicatedStorage = game:GetService("ReplicatedStorage")
	local remotesFolder = ReplicatedStorage:FindFirstChild("Remotes")
	if not remotesFolder then
		remotesFolder = Instance.new("Folder")
		remotesFolder.Name = "Remotes"
		remotesFolder.Parent = ReplicatedStorage
	end

	local teleportDataRemote = remotesFolder:FindFirstChild("TeleportDataReceived")
	if not teleportDataRemote then
		teleportDataRemote = Instance.new("RemoteEvent")
		teleportDataRemote.Name = "TeleportDataReceived"
		teleportDataRemote.Parent = remotesFolder
	end

	-- 监听客户端发送的传输数据
	teleportDataRemote.OnServerEvent:Connect(function(player: Player, teleportData: TeleportData?)
		self:ReceiveTeleportDataFromClient(player, teleportData)
	end)

	-- 监听玩家加入事件，直接从TeleportService获取数据
	Players.PlayerAdded:Connect(function(player: Player)
		-- 尝试直接从TeleportService获取传送数据
		local teleportData = TeleportService:GetTeleportData()

		if teleportData then
			print("TeleportDataManager: 检测到传送数据，处理玩家 " .. player.Name)
			-- 直接处理传送数据
			self:ReceiveTeleportDataFromClient(player, teleportData)
		else
			print("TeleportDataManager: 玩家 " .. player.Name .. " 直接加入（无传送数据）")
			-- 标记为没有传送数据
			self:ReceiveTeleportDataFromClient(player, nil)
		end
	end)

	-- 监听玩家离开事件，清理数据
	Players.PlayerRemoving:Connect(function(player: Player)
		self:CleanupPlayerData(player)
	end)

	print("TeleportDataManager 初始化完成，RemoteEvent已设置")
end

-- 调试函数：手动设置玩家传输数据（用于测试）
function TeleportDataManager:SetTestTeleportData(player: Player, testData: TeleportData): boolean
	if not player or not testData then
		warn("SetTestTeleportData: 缺少必要参数")
		return false
	end

	local isValid, errorMsg = validateTeleportData(testData)
	if not isValid then
		warn("测试数据格式无效: " .. errorMsg)
		return false
	end

	playerTeleportData[player.UserId] = testData

	return true
end

-- 获取所有有传输数据的玩家
function TeleportDataManager:GetPlayersWithTeleportData()
	local players = {}
	for userId, data in pairs(playerTeleportData) do
		local player = Players:GetPlayerByUserId(userId)
		if player then
			table.insert(players, {
				player = player,
				data = data
			})
		end
	end
	return players
end

-- 新增：等待玩家传输数据就绪
function TeleportDataManager:WaitForPlayerDataReady(player: Player, timeoutSeconds: number?): DataStatus
	timeoutSeconds = timeoutSeconds or 8 -- 默认8秒超时

	-- 检查是否已经有数据状态
	local currentStatus = playerDataStatus[player.UserId]
	if currentStatus == "received" or currentStatus == "no_data" then
		print("玩家 " .. player.Name .. " 数据已就绪，状态: " .. currentStatus)
		return currentStatus
	end

	-- 标记为等待状态
	playerDataStatus[player.UserId] = "waiting"
	print("开始等待玩家 " .. player.Name .. " 的传输数据，超时时间: " .. timeoutSeconds .. "秒")

	-- 创建超时计时器
	local timeoutReached = false
	spawn(function()
		wait(timeoutSeconds)
		if playerDataStatus[player.UserId] == "waiting" then
			playerDataStatus[player.UserId] = "timeout"
			timeoutReached = true
			dataReadyEvent:Fire(player, "timeout")
			print("玩家 " .. player.Name .. " 传输数据等待超时")
		end
	end)

	-- 等待数据就绪事件
	local connection
	local dataStatus: DataStatus? = nil

	connection = dataReadyEvent.Event:Connect(function(eventPlayer: Player, status: DataStatus)
		if eventPlayer == player then
			dataStatus = status
			connection:Disconnect()
		end
	end)

	-- 等待直到有结果
	while not dataStatus and not timeoutReached do
		wait(0.1)
	end

	-- 清理连接
	if connection then
		connection:Disconnect()
	end

	local finalStatus = playerDataStatus[player.UserId] or "timeout"
	print("玩家 " .. player.Name .. " 数据等待完成，最终状态: " .. finalStatus)
	return finalStatus
end

-- 新增：获取玩家数据状态
function TeleportDataManager:GetPlayerDataStatus(player: Player): DataStatus
	return playerDataStatus[player.UserId] or "unknown"
end

-- 新增：手动设置玩家为等待状态（用于预处理）
function TeleportDataManager:SetPlayerWaiting(player: Player)
	playerDataStatus[player.UserId] = "waiting"
	print("设置玩家 " .. player.Name .. " 为等待传输数据状态")
end

return TeleportDataManager
