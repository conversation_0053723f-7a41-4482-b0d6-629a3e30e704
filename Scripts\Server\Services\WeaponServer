local ReplicatedStorage = game:GetService("ReplicatedStorage")
local SoundService = game:GetService("SoundService")
local Players = game:GetService("Players")
local WeaponConfig = require(ReplicatedStorage.Scripts.Config.WeaponConfig)
local MeleeConfig = require(ReplicatedStorage.Scripts.Config.MeleeConfig) -- 添加近战武器配置
local RemoteWeaponConfig = require(ReplicatedStorage.Scripts.Config.RemoteWeaponConfig) -- 添加远程武器配置
local SkillConfig = require(ReplicatedStorage.Scripts.Config.SkillConfig)
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local AmmoInventoryServer = require(ReplicatedStorage.Scripts.Server.Services.AmmoInventoryServer)

local WeaponServer = {}
local playerAttackTimestamps = {}
local playerWeaponData = {} -- 存储玩家当前装备的武器数据
local playerMeleeData = {} -- 存储玩家当前装备的近战武器数据
local playerRemoteData = {} -- 存储玩家当前装备的远程武器数据
local playerSkillCooldowns = {} -- 存储玩家技能冷却状态

-- 全局武器实例状态管理（新增）
-- 用于跟踪每个武器实例的真实状态，确保多玩家间同步
local globalWeaponInstances = {}

-- 换弹任务管理（新增）
-- 用于跟踪和管理正在进行的换弹任务，确保状态一致性
local playerReloadTasks = {} -- 存储玩家的换弹任务


-- 根据工具名称查找武器配置
function WeaponServer:FindWeaponDataByName(modelName)
	for _, weaponData in ipairs(WeaponConfig) do
		if weaponData.WeaponModeling == modelName then
			return weaponData
		end
	end
	return nil
end

-- 根据武器ID查找近战武器配置
function WeaponServer:FindMeleeDataById(weaponId)
	for _, meleeData in ipairs(MeleeConfig) do
		if meleeData.Id == weaponId then
			return meleeData
		end
	end
	return nil
end

-- 根据武器ID查找远程武器配置
function WeaponServer:FindRemoteDataById(weaponId)
	for _, remoteData in ipairs(RemoteWeaponConfig) do
		if remoteData.Id == weaponId then
			return remoteData
		end
	end
	return nil
end

-- 根据ID查找技能配置
function WeaponServer:FindSkillDataById(skillId)
	for _, skillData in ipairs(SkillConfig) do
		if skillData.Id == skillId then
			return skillData
		end
	end
	return nil
end

-- 生成武器实例唯一标识符
function WeaponServer:GenerateWeaponInstanceId(weaponName)
	return weaponName .. "_" .. tostring(tick()) .. "_" .. tostring(math.random(1000, 9999))
end

-- 从工具获取或创建实例ID
function WeaponServer:GetOrCreateWeaponInstanceId(tool)
	if not tool then return nil end

	-- 检查工具是否已有实例ID
	local instanceIdValue = tool:FindFirstChild("WeaponInstanceId")
	if instanceIdValue and instanceIdValue:IsA("StringValue") then
		return instanceIdValue.Value
	end

	-- 创建新的实例ID
	local instanceId = self:GenerateWeaponInstanceId(tool.Name)
	local idValue = Instance.new("StringValue")
	idValue.Name = "WeaponInstanceId"
	idValue.Value = instanceId
	idValue.Parent = tool

	print("为武器创建实例ID: " .. tool.Name .. " -> " .. instanceId)
	return instanceId
end

-- 获取全局武器实例状态
function WeaponServer:GetGlobalWeaponInstanceState(weaponInstanceId)
	if not weaponInstanceId then return nil end
	return globalWeaponInstances[weaponInstanceId]
end

-- 更新全局武器实例状态
function WeaponServer:UpdateGlobalWeaponInstanceState(weaponInstanceId, stateData)
	if not weaponInstanceId then return end

	if not globalWeaponInstances[weaponInstanceId] then
		globalWeaponInstances[weaponInstanceId] = {}
	end

	-- 更新状态数据
	for key, value in pairs(stateData) do
		globalWeaponInstances[weaponInstanceId][key] = value
	end

	-- 添加时间戳
	globalWeaponInstances[weaponInstanceId].lastUpdated = tick()

	print("更新全局武器实例状态: " .. weaponInstanceId .. ", 弹药: " .. (stateData.currentAmmo or "未知"))
end

-- 清理过期的武器实例状态（可选，防止内存泄漏）
function WeaponServer:CleanupExpiredWeaponInstances()
	local currentTime = tick()
	local expireTime = 3600 -- 1小时后过期

	for instanceId, state in pairs(globalWeaponInstances) do
		if state.lastUpdated and (currentTime - state.lastUpdated) > expireTime then
			globalWeaponInstances[instanceId] = nil
			print("清理过期武器实例状态: " .. instanceId)
		end
	end
end

-- 创建换弹任务
function WeaponServer:CreateReloadTask(player, weaponId, taskData)
	if not playerReloadTasks[player.UserId] then
		playerReloadTasks[player.UserId] = {}
	end

	local taskId = weaponId .. "_" .. tostring(tick())
	playerReloadTasks[player.UserId][weaponId] = {
		taskId = taskId,
		weaponId = weaponId,
		startTime = tick(),
		ammoNeeded = taskData.ammoNeeded,
		ammoId = taskData.ammoId,
		reloadTime = taskData.reloadTime,
		isActive = true
	}

	print("创建换弹任务: " .. player.Name .. ", 武器ID: " .. weaponId .. ", 任务ID: " .. taskId)
	return taskId
end

-- 取消换弹任务
function WeaponServer:CancelReloadTask(player, weaponId)
	if not playerReloadTasks[player.UserId] then return false end

	local task = playerReloadTasks[player.UserId][weaponId]
	if task and task.isActive then
		task.isActive = false
		playerReloadTasks[player.UserId][weaponId] = nil
		print("取消换弹任务: " .. player.Name .. ", 武器ID: " .. weaponId)
		return true
	end

	return false
end

-- 获取换弹任务
function WeaponServer:GetReloadTask(player, weaponId)
	if not playerReloadTasks[player.UserId] then return nil end
	return playerReloadTasks[player.UserId][weaponId]
end

-- 清理玩家所有换弹任务
function WeaponServer:ClearPlayerReloadTasks(player)
	if playerReloadTasks[player.UserId] then
		for weaponId, task in pairs(playerReloadTasks[player.UserId]) do
			if task.isActive then
				task.isActive = false
				print("清理换弹任务: " .. player.Name .. ", 武器ID: " .. weaponId)
			end
		end
		playerReloadTasks[player.UserId] = {}
	end
end

-- 玩家装备武器时，更新其武器数据
function WeaponServer:UpdatePlayerWeapon(player, toolName)
	local weaponData = self:FindWeaponDataByName(toolName)
	if weaponData then
		playerWeaponData[player] = weaponData
		playerSkillCooldowns[player] = {
			[1] = 0,
			[2] = 0,
			[3] = 0
		}

		-- 根据武器类型加载对应配置
		if weaponData.WeaponType == 1 then -- 近战武器
			playerMeleeData[player] = self:FindMeleeDataById(weaponData.Id)
			playerRemoteData[player] = nil
			print("服务端更新玩家 " .. player.Name .. " 的近战武器数据: " .. weaponData.Name)
		elseif weaponData.WeaponType == 2 then -- 远程武器
			playerMeleeData[player] = nil
			local remoteData = self:FindRemoteDataById(weaponData.Id)
			playerRemoteData[player] = remoteData
			print("服务端更新玩家 " .. player.Name .. " 的远程武器数据: " .. weaponData.Name .. ", 射击模式: " .. (remoteData and remoteData.WeaponType or "未知"))

			-- 获取当前装备的工具实例
			local tool = nil
			if player.Character then
				tool = player.Character:FindFirstChild(toolName)
			end
			if not tool and player:FindFirstChild("Backpack") then
				tool = player.Backpack:FindFirstChild(toolName)
			end

			-- 获取或创建武器实例ID
			local weaponInstanceId = nil
			if tool then
				weaponInstanceId = self:GetOrCreateWeaponInstanceId(tool)
			end

			-- 检查是否有保存的弹药状态
			local savedAmmo = nil

			-- 第一优先级：从全局武器实例状态获取（确保多玩家同步）
			if weaponInstanceId then
				local globalState = self:GetGlobalWeaponInstanceState(weaponInstanceId)
				if globalState and globalState.currentAmmo then
					savedAmmo = globalState.currentAmmo
					print("从全局武器实例状态读取弹药: " .. weaponInstanceId .. " -> " .. savedAmmo)
				end
			end

			-- 第二优先级：从工具上的SavedAmmo值读取（用于死亡盒子恢复）
			if not savedAmmo and tool then
				local ammoValue = tool:FindFirstChild("SavedAmmo")
				if ammoValue and ammoValue:IsA("IntValue") then
					savedAmmo = ammoValue.Value
					print("从工具中读取弹药状态: " .. savedAmmo)
					ammoValue:Destroy()
				end
			end

			-- 第三优先级：从玩家保存状态中查找（向后兼容）
			if not savedAmmo and weaponInstanceId and self.SavedWeaponAmmo and self.SavedWeaponAmmo[player.UserId] then
				local savedState = self.SavedWeaponAmmo[player.UserId][weaponInstanceId]
				if savedState then
					-- 兼容两种保存格式：对象格式和数值格式
					if type(savedState) == "table" and savedState.savedAmmo then
						savedAmmo = savedState.savedAmmo
						print("找到保存的弹药状态(对象格式): " .. weaponInstanceId .. ", 弹药: " .. savedAmmo)
					elseif type(savedState) == "number" then
						savedAmmo = savedState
						print("找到保存的弹药状态(数值格式): " .. weaponInstanceId .. ", 弹药: " .. savedAmmo)
					end

					-- 使用后清除保存的状态
					self.SavedWeaponAmmo[player.UserId][weaponInstanceId] = nil
				end
			end

			-- 设置初始弹药状态（修复：即使保存的弹药为0也要使用）
			if savedAmmo ~= nil and savedAmmo >= 0 then
				weaponData.currentAmmo = savedAmmo
				print("使用保存的弹药状态: " .. savedAmmo)
			else
				-- 检查武器是否有InitialAmmo属性（来自新手盒子）
				local initialAmmo = nil
				local tool = nil

				-- 首先尝试从角色中查找装备的工具
				if player.Character then
					tool = player.Character:FindFirstChildOfClass("Tool")
					if tool and tool.Name == toolName then
						local initialAmmoValue = tool:FindFirstChild("InitialAmmo")
						if initialAmmoValue and initialAmmoValue:IsA("IntValue") then
							initialAmmo = initialAmmoValue.Value
							print("从装备武器InitialAmmo属性获取初始弹药: " .. initialAmmo)
							-- 使用后删除属性，避免重复使用
							initialAmmoValue:Destroy()
						end
					end
				end

				-- 如果角色中没找到，尝试从背包中查找
				if not initialAmmo and player:FindFirstChild("Backpack") then
					for _, backpackTool in pairs(player.Backpack:GetChildren()) do
						if backpackTool:IsA("Tool") and backpackTool.Name == toolName then
							local initialAmmoValue = backpackTool:FindFirstChild("InitialAmmo")
							if initialAmmoValue and initialAmmoValue:IsA("IntValue") then
								initialAmmo = initialAmmoValue.Value
								print("从背包武器InitialAmmo属性获取初始弹药: " .. initialAmmo)
								-- 使用后删除属性，避免重复使用
								initialAmmoValue:Destroy()
								break
							end
						end
					end
				end

				-- 使用InitialAmmo或默认满弹夹
				weaponData.currentAmmo = initialAmmo or (remoteData and remoteData.AmmoCapacity or 30)
				print("设置武器初始弹药: " .. weaponData.currentAmmo .. " (来源: " .. (initialAmmo and "InitialAmmo" or "默认值") .. ")")
			end

			-- 更新全局武器实例状态（关键：确保多玩家同步）
			if weaponInstanceId then
				self:UpdateGlobalWeaponInstanceState(weaponInstanceId, {
					currentAmmo = weaponData.currentAmmo,
					weaponName = toolName,
					weaponId = weaponData.Id
				})
			end
		else
			-- 无效武器类型
			playerMeleeData[player] = nil
			playerRemoteData[player] = nil
			print("无效的武器类型: " .. (weaponData.WeaponType or "未知"))
		end

		-- 通知客户端武器已装备，只传递图标信息用于UI显示
		NotifyService.FireClient("WeaponEquipped", player, {
			weaponId = weaponData.Id,
			weaponName = weaponData.Name,
			weaponIcon = weaponData.Icon,  -- 这个Icon只用于UI显示
			weaponType = weaponData.WeaponType
		})

		-- 如果是远程武器，同时发送弹药更新
		if weaponData.WeaponType == 2 and playerRemoteData[player] then
			NotifyService.FireClient("AmmoUpdated", player, {
				weaponId = weaponData.Id,
				remoteWeaponId = playerRemoteData[player].Id,
				currentAmmo = weaponData.currentAmmo
			})
		end
	end
end

-- 玩家卸下武器时，清除其武器数据
function WeaponServer:ClearPlayerWeapon(player)
	-- 如果玩家正在换弹，先通知客户端取消换弹状态
	local weaponData = playerWeaponData[player]
	if weaponData and weaponData.isReloading then
		print("玩家卸下武器时取消换弹状态: " .. player.Name)

		-- 取消对应的换弹任务
		self:CancelReloadTask(player, weaponData.Id)

		-- 重置武器换弹状态
		weaponData.isReloading = false

		NotifyService.FireClient("ReloadCancelled", player, {})
	end

	playerWeaponData[player] = nil
	playerMeleeData[player] = nil
	playerRemoteData[player] = nil
	playerSkillCooldowns[player] = nil
end

-- 检查技能是否在冷却中
function WeaponServer:IsSkillOnCooldown(player, skillIndex)
	if not playerSkillCooldowns[player] then
		playerSkillCooldowns[player] = {[1] = 0, [2] = 0, [3] = 0}
	end

	local now = tick()
	local lastUseTime = playerSkillCooldowns[player][skillIndex] or 0

	-- 获取技能数据
	local weaponData = playerWeaponData[player]
	if not weaponData then return true end

	local skillId = nil
	if weaponData.WeaponType == 1 and playerMeleeData[player] then -- 近战武器
		local meleeData = playerMeleeData[player]
		if skillIndex == 1 then
			skillId = meleeData.FirstAttackSkill
		elseif skillIndex == 2 then
			skillId = meleeData.SecondAttackSkill
		elseif skillIndex == 3 then
			skillId = meleeData.ThirdAttackSkill
		end
	elseif weaponData.WeaponType == 2 and playerRemoteData[player] then -- 远程武器
		-- 远程武器暂时不处理技能冷却
		return false
	end

	if not skillId then return true end

	local skillData = self:FindSkillDataById(skillId)
	if not skillData then return true end

	local cooldownTime = skillData.Cooldown or 0.5
	return (now - lastUseTime) < cooldownTime
end

-- 通知玩家技能冷却
function WeaponServer:NotifySkillCooldown(player, skillIndex)
	if not player or not playerWeaponData[player] then return end

	local weaponData = playerWeaponData[player]
	local skillId = nil

	if weaponData.WeaponType == 1 and playerMeleeData[player] then -- 近战武器
		local meleeData = playerMeleeData[player]
		if skillIndex == 1 then
			skillId = meleeData.FirstAttackSkill
		elseif skillIndex == 2 then
			skillId = meleeData.SecondAttackSkill
		elseif skillIndex == 3 then
			skillId = meleeData.ThirdAttackSkill
		end
	elseif weaponData.WeaponType == 2 and playerRemoteData[player] then -- 远程武器
		-- 远程武器暂时不处理技能冷却
		return
	end

	if not skillId then return end

	local skillData = self:FindSkillDataById(skillId)
	if not skillData then return end

	local now = tick()
	local lastUseTime = playerSkillCooldowns[player][skillIndex] or 0
	local cooldownTime = skillData.Cooldown or 0.5
	local remainingTime = cooldownTime - (now - lastUseTime)

	if remainingTime > 0 then
		NotifyService.FireClient("WeaponSkillCooldown", player, {
			skillIndex = skillIndex,
			remainingTime = remainingTime,
			cooldownTime = cooldownTime
		})
	end
end

function WeaponServer:HandleAttack(player, data)
	-- 获取玩家当前武器数据
	local weaponData = playerWeaponData[player]
	if not weaponData then
		-- 尝试从角色中获取当前装备的工具
		local character = player.Character
		if character then
			for _, child in pairs(character:GetChildren()) do
				if child:IsA("Tool") then
					weaponData = self:FindWeaponDataByName(child.Name)
					if weaponData then
						playerWeaponData[player] = weaponData
						playerSkillCooldowns[player] = {[1] = 0, [2] = 0, [3] = 0}

						-- 根据武器类型加载对应配置
						if weaponData.WeaponType == 1 then -- 近战武器
							playerMeleeData[player] = self:FindMeleeDataById(weaponData.Id)
							playerRemoteData[player] = nil
						elseif weaponData.WeaponType == 2 then -- 远程武器
							playerMeleeData[player] = nil
							playerRemoteData[player] = self:FindRemoteDataById(weaponData.Id)
						end

						break
					end
				end
			end
		end

		if not weaponData then return end -- 仍然没有找到武器数据，退出
	end

	-- 检查技能是否在冷却中
	if self:IsSkillOnCooldown(player, data.skillIndex) then
		-- 通知客户端技能冷却中
		self:NotifySkillCooldown(player, data.skillIndex)
		return -- 技能冷却中
	end

	-- 更新技能冷却
	if not playerSkillCooldowns[player] then
		playerSkillCooldowns[player] = {[1] = 0, [2] = 0, [3] = 0}
	end
	playerSkillCooldowns[player][data.skillIndex] = tick()

	local character = player.Character
	if not character or not character:FindFirstChild("HumanoidRootPart") then return end

	-- 获取角色信息和朝向
	local root = character.HumanoidRootPart
	local characterCFrame = root.CFrame
	local lookVector = characterCFrame.LookVector

	-- 根据武器类型处理攻击
	if weaponData.WeaponType == 1 and playerMeleeData[player] then -- 近战武器
		self:HandleMeleeAttack(player, data, root, lookVector)
	elseif weaponData.WeaponType == 2 and playerRemoteData[player] then -- 远程武器
		self:HandleRangedAttack(player, data, root, lookVector)
	else
		print("无法处理攻击，无效的武器类型或缺少武器数据")
	end

	-- 播放攻击特效 - 通知所有客户端
	if data.effectName and data.effectName ~= "" then
		NotifyService.FireAllClient("PlayAttackEffect", {
			effectName = data.effectName,
			position = data.position,
			direction = data.direction,
			playerName = player.Name
		})
	end
end

-- 处理近战武器攻击
function WeaponServer:HandleMeleeAttack(player, data, root, lookVector)
	local weaponData = playerWeaponData[player]
	local meleeData = playerMeleeData[player]
	if not meleeData then return end

	-- 获取近战武器攻击范围参数
	local attackRange = meleeData.AttackRange
	local rangeX = attackRange.X
	local rangeY = attackRange.Y
	local rangeZ = attackRange.Z

	-- 计算攻击区域中心点（在角色前方）
	local regionCenter = root.Position + lookVector * (rangeZ/2)

	-- 创建攻击区域的边界框
	local regionCFrame = CFrame.new(regionCenter, regionCenter + lookVector) -- 朝向角色面对的方向
	local regionSize = Vector3.new(rangeX, rangeY, rangeZ)

	-- 可视化攻击范围（仅调试用）
	local debugMode = true -- 可以设置为配置项
	if debugMode then
		local debugPart = Instance.new("Part")
		debugPart.Anchored = true
		debugPart.CanCollide = false
		debugPart.Transparency = 0.7
		debugPart.Shape = Enum.PartType.Block
		debugPart.Size = regionSize
		debugPart.CFrame = regionCFrame
		debugPart.Material = Enum.Material.Neon
		debugPart.BrickColor = BrickColor.new("Really blue")
		debugPart.Parent = workspace
		game:GetService("Debris"):AddItem(debugPart, 0.1)
	end

	-- 查找当前技能对应的技能ID
	local skillId = nil
	if data.skillIndex == 1 then
		skillId = meleeData.FirstAttackSkill
	elseif data.skillIndex == 2 then
		skillId = meleeData.SecondAttackSkill
	elseif data.skillIndex == 3 then
		skillId = meleeData.ThirdAttackSkill
	end

	-- 获取技能数据
	local skillData = skillId and self:FindSkillDataById(skillId)
	if not skillData then
		return -- 无效的技能ID
	end

	-- 计算伤害
	local baseDamage = meleeData.AttackValue
	local finalDamage = baseDamage * (skillData.HitDamage or 1.0) -- 使用命中伤害倍数

	-- 对于不同技能可以设置不同效果
	if data.skillIndex == 2 then
		-- 第二技能可能有特殊效果，如范围伤害加成
		finalDamage = finalDamage * 1.5
	elseif data.skillIndex == 3 then
		-- 第三技能可能是终结技，伤害更高
		finalDamage = finalDamage * 2.0
	end

	-- 检测攻击区域内的角色（仅保留敌人）
	local overlapParams = OverlapParams.new()
	overlapParams.FilterDescendantsInstances = {player.Character} -- 排除自己
	overlapParams.FilterType = Enum.RaycastFilterType.Exclude

	local partsInRegion = workspace:GetPartBoundsInBox(regionCFrame, regionSize, overlapParams)
	local damagedHumanoids = {}
	local monsterFolder = workspace:FindFirstChild("monster") -- 敌人根文件夹

	for _, part in pairs(partsInRegion) do
		local model = part:FindFirstAncestorOfClass("Model")
		if model and not damagedHumanoids[model] then
			-- 1. 排除所有玩家角色（包括队友）
			local isPlayer = false
			for _, p in ipairs(Players:GetPlayers()) do
				if p.Character == model then
					isPlayer = true
					break
				end
			end

			-- 使用条件判断替代goto
			if not isPlayer then
				-- 2. 仅对monster文件夹中的敌人造成伤害
				if monsterFolder and model:IsDescendantOf(monsterFolder) then
					local humanoid = model:FindFirstChildOfClass("Humanoid")
					if humanoid and humanoid.Health > 0 then
						-- 对敌人造成伤害
						humanoid:TakeDamage(finalDamage)
						damagedHumanoids[model] = true
						print("近战攻击命中敌人（" .. model.Name .. "），造成伤害：" .. finalDamage)

						-- 播放命中音效
						if skillData.HitSoundEffects and skillData.HitSoundEffects ~= "" then
							-- 创建服务端音效并播放
							local sound = Instance.new("Sound")
							sound.SoundId = skillData.HitSoundEffects
							sound.Volume = 1
							sound.Parent = model.HumanoidRootPart
							sound:Play()
							game:GetService("Debris"):AddItem(sound, 3)
						end

						-- 可以在这里添加击中特效或击退等效果
						if skillData.HasKnockback and model:FindFirstChild("HumanoidRootPart") then
							local knockbackDirection = (model.HumanoidRootPart.Position - root.Position).Unit
							knockbackDirection = Vector3.new(knockbackDirection.X, 0, knockbackDirection.Z).Unit
							local knockbackForce = skillData.KnockbackForce or 0

							if knockbackForce > 0 then
								-- 添加力以实现击退
								local bodyVelocity = Instance.new("BodyVelocity")
								bodyVelocity.MaxForce = Vector3.new(100000, 100000, 100000)
								bodyVelocity.Velocity = knockbackDirection * knockbackForce
								bodyVelocity.Parent = model.HumanoidRootPart

								game:GetService("Debris"):AddItem(bodyVelocity, 0.2)
							end
						end
					end
				end
			end
		end
	end
end
-- 处理远程武器攻击
function WeaponServer:HandleRangedAttack(player, data, root, lookVector)
	local weaponData = playerWeaponData[player]
	local remoteData = playerRemoteData[player]

	if not remoteData then return end

	-- 使用客户端提供的发射位置和方向
	local startPosition = data.position or root.Position + Vector3.new(0, 0.5, 0) + lookVector * 2
	local direction = data.direction or lookVector

	-- 同步弹药数
	if data.currentAmmo ~= nil then
		-- 注意：射击时不消耗弹药库中的弹药，只在换弹时消耗

		-- 向客户端更新弹药状态
		NotifyService.FireClient("AmmoUpdated", player, {
			weaponId = weaponData.Id,
			remoteWeaponId = remoteData.Id,
			currentAmmo = data.currentAmmo
		})

		-- 更新服务器端的弹药数据
		weaponData.currentAmmo = data.currentAmmo

		-- 更新全局武器实例状态（关键：确保多玩家同步）
		local weaponInstanceId = data.weaponInstanceId
		if not weaponInstanceId and player.Character then
			-- 尝试从角色中获取武器实例ID
			local tool = player.Character:FindFirstChild(weaponData.Name)
			if tool then
				weaponInstanceId = self:GetOrCreateWeaponInstanceId(tool)
			end
		end

		if weaponInstanceId then
			self:UpdateGlobalWeaponInstanceState(weaponInstanceId, {
				currentAmmo = data.currentAmmo,
				weaponId = weaponData.Id
			})
			print("服务端更新全局武器实例状态: " .. weaponInstanceId .. ", 弹药: " .. data.currentAmmo)
		end

		print("服务端更新玩家 " .. player.Name .. " 的弹药数: " .. data.currentAmmo)
	end

	-- 确保所有子弹参数都有默认值
	local bulletModel = data.bulletModel or remoteData.BallisticModel or "Bullet_01"
	local bulletType = remoteData.BallisticType or "BasePart" -- 新增：子弹类型
	local speed = data.speed or remoteData.BallisticSpeed or 100
	local range = data.range or remoteData.Range or 100
	local damage = data.damage or remoteData.Damage or 10
	local weaponId = data.weaponId or (weaponData and weaponData.Id) or 0
	local remoteWeaponId = data.remoteWeaponId or (remoteData and remoteData.Id) or 0

	-- 广播子弹创建事件给所有其他玩家 (传递更完整的数据)
	NotifyService.FireAllExceptClient("BulletCreated", player, {
		bulletModel = bulletModel,
		bulletType = bulletType, -- 新增：子弹类型
		startPosition = startPosition,
		direction = direction,
		speed = speed,
		range = range,
		damage = damage,
		weaponId = weaponId,
		remoteWeaponId = remoteWeaponId,
		playerName = player.Name
	})

	-- 不再使用射线检测造成伤害，改为基于客户端子弹实际碰撞发送的事件处理
	-- 原射线检测代码已移除
end

function WeaponServer:HandleBulletHit(player, data)
	-- 验证数据有效性
	if not data.targetName or not data.position then
		warn("BulletHit 缺少必要参数: targetName 或 position")
		return
	end

	local damage = data.damage or 10
	if not damage or damage <= 0 then
		damage = 10
	end

	-- 查找目标：仅在workspace.monster文件夹中查找
	local monsterFolder = workspace:FindFirstChild("monster")
	if not monsterFolder then return end

	-- 查找所有可能的目标（同名敌人）
	local potentialTargets = {}

	-- 递归查找所有同名模型
	local function findAllByName(parent, name, results)
		for _, child in ipairs(parent:GetChildren()) do
			if child:IsA("Model") and child.Name == name then
				table.insert(results, child)
			elseif child:IsA("Folder") or child:IsA("Model") then
				findAllByName(child, name, results)
			end
		end
	end

	findAllByName(monsterFolder, data.targetName, potentialTargets)

	-- 如果没有找到目标，退出
	if #potentialTargets == 0 then
		print("未找到目标: " .. data.targetName)
		return
	end

	-- 如果只有一个目标，直接使用
	if #potentialTargets == 1 then
		local targetModel = potentialTargets[1]
		local humanoid = targetModel:FindFirstChildOfClass("Humanoid")
		if humanoid and humanoid.Health > 0 then
			-- 仅对敌人造成伤害
			humanoid:TakeDamage(damage)
			print("子弹命中敌人（" .. targetModel.Name .. "），造成伤害：" .. damage)
			return
		end
	else
		-- 有多个同名目标，使用位置信息找到最接近的目标
		local closestTarget = nil
		local closestDistance = math.huge
		local hitPosition = data.targetPosition or data.position

		for _, target in ipairs(potentialTargets) do
			local primaryPart = target.PrimaryPart or target:FindFirstChild("HumanoidRootPart")
			if primaryPart then
				local distance = (primaryPart.Position - hitPosition).Magnitude
				if distance < closestDistance then
					closestDistance = distance
					closestTarget = target
				end
			end
		end

		-- 对最接近的目标造成伤害
		if closestTarget then
			local humanoid = closestTarget:FindFirstChildOfClass("Humanoid")
			if humanoid and humanoid.Health > 0 then
				humanoid:TakeDamage(damage)
				print("子弹命中敌人（" .. closestTarget.Name .. "），造成伤害：" .. damage)
				return
			end
		end
	end

	local targetModel = nil

	-- 优先从monster文件夹查找（确保是敌人）
	if monsterFolder then
		-- 支持精确匹配或子文件夹查找
		targetModel = monsterFolder:FindFirstChild(data.targetName)
		-- 如果直接找不到，递归查找子文件夹（应对动态生成在子文件夹的敌人）
		if not targetModel then
			local function findInChildren(parent)
				for _, child in ipairs(parent:GetChildren()) do
					if child:IsA("Model") and child.Name == data.targetName then
						targetModel = child
						return
					end
					if child:IsA("Folder") then
						findInChildren(child)
					end
				end
			end
			findInChildren(monsterFolder)
		end
	end

	-- 验证目标是否为有效敌人（必须满足：在monster文件夹+有Humanoid+存活）
	if targetModel and targetModel:IsDescendantOf(monsterFolder) then
		local humanoid = targetModel:FindFirstChildOfClass("Humanoid")
		if humanoid and humanoid.Health > 0 then
			-- 仅对敌人造成伤害
			humanoid:TakeDamage(damage)
			print("子弹命中敌人（" .. targetModel.Name .. "），造成伤害：" .. damage)
			return
		end
	end

	-- 未找到有效敌人（或命中玩家/其他物体）
	print("未对目标造成伤害（非有效敌人）：" .. tostring(data.targetName))
end


-- 处理玩家换弹
function WeaponServer:HandleReload(player, data)
	-- 处理不同类型的参数
	local weaponId
	if type(data) == "table" then
		weaponId = data.weaponId
	else
		weaponId = data -- 直接传入了weaponId
	end

	print("收到玩家换弹请求: " .. player.Name .. ", 武器ID: " .. tostring(weaponId))

	-- 如果没有提供武器ID，尝试获取当前装备的武器
	if not weaponId or weaponId == 0 then
		print("未提供武器ID，尝试从玩家角色获取当前武器")
		local character = player.Character
		if character then
			for _, child in pairs(character:GetChildren()) do
				if child:IsA("Tool") then
					local weaponData = self:FindWeaponDataByName(child.Name)
					if weaponData then
						weaponId = weaponData.Id
						print("从角色中找到武器: " .. child.Name .. ", ID: " .. weaponId)
						break
					end
				end
			end
		end
	end

	-- 获取武器数据
	local weaponData = self:GetPlayerWeaponData(player, weaponId)
	if not weaponData then 
		print("无法找到武器数据，weaponId:", weaponId)
		return false 
	end

	print("武器类型: " .. weaponData.WeaponType)
	if weaponData.WeaponType ~= 2 then
		print("只有远程武器(类型2)可以换弹，当前是: " .. weaponData.WeaponType)
		return false
	end

	-- 获取远程武器数据
	if not weaponData.remoteWeaponId then
		print("远程武器ID为nil，尝试找到一个匹配的远程武器配置")
		-- 尝试查找匹配的远程武器配置
		for _, config in ipairs(RemoteWeaponConfig) do
			if config.Id == weaponId then
				weaponData.remoteWeaponId = config.Id
				print("找到匹配的远程武器配置: ID=" .. config.Id)
				break
			end
		end

		-- 如果仍然找不到，使用第一个远程武器配置作为默认
		if not weaponData.remoteWeaponId and #RemoteWeaponConfig > 0 then
			weaponData.remoteWeaponId = RemoteWeaponConfig[1].Id
			print("使用默认远程武器配置: ID=" .. RemoteWeaponConfig[1].Id)
		end
	end

	local remoteData = self:GetRemoteWeaponData(weaponData.remoteWeaponId)
	if not remoteData then 
		print("无法找到远程武器数据，武器ID: " .. weaponId .. ", 远程武器ID: " .. (weaponData.remoteWeaponId or "nil"))

		-- 最后尝试：使用第一个可用的远程武器配置
		if #RemoteWeaponConfig > 0 then
			remoteData = RemoteWeaponConfig[1]
			weaponData.remoteWeaponId = remoteData.Id
			print("使用第一个可用的远程武器配置作为后备: ID=" .. remoteData.Id)
		else
			return false
		end
	end

	print("找到远程武器数据，射击模式: " .. remoteData.WeaponType .. ", 弹药容量: " .. remoteData.AmmoCapacity)

	-- 检查是否已经满弹
	if weaponData.currentAmmo >= remoteData.AmmoCapacity then
		print("弹药已满，无需换弹")
		return false
	end

	-- 检查是否已经在换弹
	if weaponData.isReloading then
		print("正在换弹中")
		return false
	end

	-- 获取弹药ID
	local ammoId = remoteData.BallisticId
	print("获取弹药ID: " .. tostring(ammoId))

	-- 如果弹药ID为nil，使用武器ID作为弹药ID
	if not ammoId then
		ammoId = weaponId
		print("弹药ID为nil，使用武器ID代替: " .. ammoId)
	end

	-- 计算需要补充的弹药量
	local ammoNeeded = remoteData.AmmoCapacity - weaponData.currentAmmo
	print("需要补充弹药: " .. ammoNeeded .. "发")

	-- 检查玩家是否有足够的弹药
	if not AmmoInventoryServer:HasEnoughAmmo(player, ammoId, ammoNeeded) then
		-- 如果弹药不足，使用玩家拥有的所有弹药
		ammoNeeded = AmmoInventoryServer:GetPlayerAmmoAmount(player, ammoId)
		print("弹药不足，使用玩家现有弹药: " .. ammoNeeded .. "发")

		if ammoNeeded <= 0 then
			print("弹药库中没有弹药，无法换弹")
			return false
		end
	end

	-- 设置换弹状态
	weaponData.isReloading = true

	-- 创建换弹任务
	local taskId = self:CreateReloadTask(player, weaponId, {
		ammoNeeded = ammoNeeded,
		ammoId = ammoId,
		reloadTime = remoteData.ReloadTime or 2
	})

	-- 通知客户端开始换弹
	NotifyService.FireClient("ReloadStarted", player, {})
	print("开始换弹，无法射击 (x" .. ammoNeeded .. ")")

	-- 保存换弹开始时间，用于检测超时
	weaponData.reloadStartTime = tick()

	-- 延迟执行换弹完成（使用改进的任务管理）
	delay(remoteData.ReloadTime or 2, function()
		-- 检查换弹任务是否仍然有效
		local reloadTask = self:GetReloadTask(player, weaponId)
		if not reloadTask or not reloadTask.isActive then
			print("换弹任务已被取消: " .. player.Name .. ", 武器ID: " .. weaponId)
			return
		end

		-- 检查玩家是否仍然持有该武器
		local currentWeaponData = self:GetPlayerWeaponData(player, weaponId)
		if not currentWeaponData then
			print("玩家已切换武器，取消换弹: " .. player.Name)
			self:CancelReloadTask(player, weaponId)
			return
		end

		-- 检查武器换弹状态是否一致
		if not currentWeaponData.isReloading then
			print("武器换弹状态已被重置，取消换弹: " .. player.Name)
			self:CancelReloadTask(player, weaponId)
			return
		end

		-- 检查换弹是否已经超时（超过预期时间太多）
		local currentTime = tick()
		local reloadDuration = currentTime - (currentWeaponData.reloadStartTime or 0)
		local expectedDuration = reloadTask.reloadTime

		if reloadDuration > expectedDuration * 2 then
			print("换弹超时，可能是由于网络延迟或其他问题")
			currentWeaponData.isReloading = false
			self:CancelReloadTask(player, weaponId)
			NotifyService.FireClient("ReloadCancelled", player, {})
			return
		end

		-- 从弹药库中消耗弹药
		AmmoInventoryServer:RemovePlayerAmmo(player, reloadTask.ammoId, reloadTask.ammoNeeded)

		-- 更新武器弹药
		currentWeaponData.currentAmmo = currentWeaponData.currentAmmo + reloadTask.ammoNeeded
		currentWeaponData.isReloading = false

		-- 完成换弹任务
		self:CancelReloadTask(player, weaponId)

		-- 更新全局武器实例状态（关键：确保多玩家同步）
		local weaponInstanceId = nil
		if player.Character then
			local tool = player.Character:FindFirstChild(currentWeaponData.Name)
			if tool then
				weaponInstanceId = self:GetOrCreateWeaponInstanceId(tool)
			end
		end

		if weaponInstanceId then
			self:UpdateGlobalWeaponInstanceState(weaponInstanceId, {
				currentAmmo = currentWeaponData.currentAmmo,
				weaponId = currentWeaponData.Id
			})
			print("换弹完成，更新全局武器实例状态: " .. weaponInstanceId .. ", 弹药: " .. currentWeaponData.currentAmmo)
		end

		-- 通知客户端换弹完成
		NotifyService.FireClient("ReloadFinished", player, {
			newAmmo = currentWeaponData.currentAmmo
		})

		print("换弹完成，当前弹药: " .. currentWeaponData.currentAmmo)
	end)

	return true
end

-- 处理玩家装备远程武器 (已由UpdatePlayerWeapon处理弹药初始化)
function WeaponServer:HandleWeaponEquip(player, data)
	local weaponData = playerWeaponData[player]
	local remoteData = playerRemoteData[player]

	if not weaponData or not remoteData then
		return
	end

	print("处理玩家装备远程武器事件: " .. player.Name)

	-- 弹药初始化已经在UpdatePlayerWeapon中处理，这里只需确认客户端收到最新弹药状态
	NotifyService.FireClient("AmmoUpdated", player, {
		weaponId = weaponData.Id,
		remoteWeaponId = remoteData.Id,
		currentAmmo = weaponData.currentAmmo
	})
end

-- 更新工具的视觉外观
function WeaponServer:UpdateToolAppearance(player, tool, weaponData)
	if tool and weaponData and weaponData.Icon and weaponData.Icon ~= "" then
		-- 直接设置TextureId
		tool.TextureId = weaponData.Icon

		-- 通知客户端更新工具外观
		NotifyService.FireClient("UpdateToolAppearance", player, {
			toolName = tool.Name,
			iconId = weaponData.Icon
		})
	end
end

-- 获取玩家武器数据
function WeaponServer:GetPlayerWeaponData(player, weaponId)
	-- 检查玩家是否存在
	if not player then 
		print("GetPlayerWeaponData: 玩家不存在") 
		return nil 
	end

	print("尝试获取玩家武器数据, 玩家: " .. player.Name .. ", 武器ID: " .. tostring(weaponId))

	-- 从内存中获取武器数据
	local weaponData = playerWeaponData[player]
	if weaponData and weaponData.Id == weaponId then
		print("从内存中找到武器数据: " .. weaponData.Name)
		return weaponData
	else
		print("内存中没有找到匹配的武器数据")
	end

	-- 如果有武器数据但ID不匹配，检查玩家当前实际装备的武器
	if weaponData then
		print("玩家当前武器ID: " .. weaponData.Id .. ", 但请求的武器ID: " .. weaponId)
		-- 强制使用当前武器数据进行换弹
		print("使用当前武器数据")
		return weaponData
	end

	-- 如果内存中没有，尝试从配置中查找
	print("尝试从配置中查找武器ID: " .. weaponId)
	for _, config in ipairs(WeaponConfig) do
		if config.Id == weaponId then
			print("从WeaponConfig找到武器: " .. config.Name)

			-- 创建武器数据
			local newWeaponData = {
				Id = config.Id,
				Name = config.Name,
				WeaponType = config.WeaponType,
				remoteWeaponId = nil,
				currentAmmo = 0,
				isReloading = false,
				lastShootTime = 0
			}

			-- 如果是远程武器，查找对应的远程武器数据
			if config.WeaponType == 2 then
				print("查找ID为 " .. config.Id .. " 的远程武器数据")
				-- 先尝试完全匹配ID
				for _, remoteConfig in ipairs(RemoteWeaponConfig) do
					if remoteConfig.Id == config.Id then
						newWeaponData.remoteWeaponId = remoteConfig.Id
						newWeaponData.currentAmmo = remoteConfig.AmmoCapacity
						print("找到完全匹配的远程武器数据, ID: " .. remoteConfig.Id .. ", 射击模式: " .. remoteConfig.WeaponType)
						break
					end
				end

				-- 如果没找到，尝试查找武器名称相同的
				if not newWeaponData.remoteWeaponId then
					for _, remoteConfig in ipairs(RemoteWeaponConfig) do
						print("检查远程武器配置: ID=" .. remoteConfig.Id .. ", 子弹=" .. remoteConfig.BallisticModel)
					end

					-- 如果仍未找到，使用第一个远程武器配置
					if not newWeaponData.remoteWeaponId and #RemoteWeaponConfig > 0 then
						local firstRemoteConfig = RemoteWeaponConfig[1]
						newWeaponData.remoteWeaponId = firstRemoteConfig.Id
						newWeaponData.currentAmmo = firstRemoteConfig.AmmoCapacity
						print("未找到匹配的远程武器数据，使用默认配置: ID=" .. firstRemoteConfig.Id)
					end
				end
			end

			-- 保存到内存
			playerWeaponData[player] = newWeaponData
			return newWeaponData
		end
	end

	print("无法找到符合的武器数据: ID=" .. weaponId)
	return nil
end

-- 获取远程武器数据
function WeaponServer:GetRemoteWeaponData(remoteWeaponId)
	-- 从配置中查找远程武器数据
	for _, config in ipairs(RemoteWeaponConfig) do
		if config.Id == remoteWeaponId then
			return config
		end
	end

	return nil
end

-- 处理射击
function WeaponServer:HandleShoot(player, weaponId, startPos, direction)
	-- 获取武器数据
	local weaponData = self:GetPlayerWeaponData(player, weaponId)
	if not weaponData then return false end

	-- 获取远程武器数据
	local remoteData = self:GetRemoteWeaponData(weaponData.remoteWeaponId)
	if not remoteData then return false end

	-- 检查弹药是否足够
	if weaponData.currentAmmo <= 0 then
		print("弹药不足，无法射击")
		return false
	end

	-- 检查是否在换弹
	if weaponData.isReloading then
		print("正在换弹，无法射击")
		return false
	end

	-- 检查射击冷却
	local currentTime = tick()
	if currentTime - weaponData.lastShootTime < remoteData.ShootCD then
		print("射击冷却中")
		return false
	end

	-- 更新最后射击时间
	weaponData.lastShootTime = currentTime

	-- 减少弹药
	weaponData.currentAmmo = weaponData.currentAmmo - 1

	-- 通知客户端弹药更新
	NotifyService.FireClient("AmmoUpdated", player, {
		currentAmmo = weaponData.currentAmmo
	})

	-- 创建子弹并处理命中
	local bulletModel = remoteData.BallisticModel or "Bullet_01"
	local bulletType = remoteData.BallisticType or "BasePart" -- 获取子弹类型
	local bulletSpeed = remoteData.BallisticSpeed or 100
	local bulletRange = remoteData.Range or 100
	local bulletDamage = remoteData.Damage or 10

	-- 通知所有客户端创建子弹
	NotifyService.FireAllClients("BulletCreated", {
		playerName = player.Name,
		bulletModel = bulletModel,
		bulletType = bulletType, -- 传递子弹类型
		startPosition = startPos,
		direction = direction,
		speed = bulletSpeed,
		range = bulletRange,
		damage = bulletDamage
	})

	-- 射线检测命中（仅保留对敌人的处理）
	local raycastParams = RaycastParams.new()
	raycastParams.FilterDescendantsInstances = {player.Character} -- 排除自己
	raycastParams.FilterType = Enum.RaycastFilterType.Exclude

	local rayResult = workspace:Raycast(startPos, direction.Unit * bulletRange, raycastParams)
	if rayResult then
		local hitPart = rayResult.Instance
		local hitModel = hitPart:FindFirstAncestorOfClass("Model")
		local monsterFolder = workspace:FindFirstChild("monster")

		-- 检查是否是玩家（包括队友）
		local isPlayer = false
		for _, p in ipairs(Players:GetPlayers()) do
			if p.Character == hitModel then
				isPlayer = true
				break
			end
		end
		if isPlayer then
			print("远程射线命中玩家，已过滤伤害")
			return
		end

		-- 仅处理monster文件夹中的敌人
		if hitModel and monsterFolder and hitModel:IsDescendantOf(monsterFolder) then
			local humanoid = hitModel:FindFirstChildOfClass("Humanoid")
			if humanoid and humanoid.Health > 0 then
				humanoid:TakeDamage(bulletDamage)
				print("远程射线命中敌人（" .. hitModel.Name .. "），造成伤害：" .. bulletDamage)
			end
		end
	end

	return true
end

-- 从部件获取玩家
function WeaponServer:GetPlayerFromPart(part)
	if not part then return nil end

	-- 查找部件所属的角色
	local character = part
	while character and not character:FindFirstChildOfClass("Humanoid") do
		character = character.Parent
	end

	if not character then return nil end

	-- 查找角色所属的玩家
	for _, player in pairs(game.Players:GetPlayers()) do
		if player.Character == character then
			return player
		end
	end

	return nil
end

-- 对玩家造成伤害
function WeaponServer:DamagePlayer(targetPlayer, damage, attackerPlayer)
	-- 直接返回，不执行任何伤害操作
	print("已禁用对玩家的伤害：" .. (targetPlayer and targetPlayer.Name or "未知玩家"))
	return
end

-- 获取玩家当前武器数据
function WeaponServer:GetPlayerCurrentWeaponData(player)
	-- 直接返回内存中保存的武器数据
	return playerWeaponData[player]
end

-- 处理武器卸下事件，保存弹药状态
function WeaponServer:HandleWeaponUnequipped(player, data)
	if not data or not data.weaponName then return end

	print("处理武器卸下事件: " .. player.Name .. ", 武器: " .. data.weaponName .. ", 保存弹药: " .. (data.savedAmmo or "未知"))

	-- 创建一个临时存储，保存武器弹药状态
	if not self.SavedWeaponAmmo then
		self.SavedWeaponAmmo = {}
	end

	-- 为每个玩家创建存储
	if not self.SavedWeaponAmmo[player.UserId] then
		self.SavedWeaponAmmo[player.UserId] = {}
	end

	-- 保存武器弹药状态（修复：即使弹药为0也要保存）
	if data.savedAmmo ~= nil and data.savedAmmo >= 0 then
		-- 如果提供了武器实例ID，使用实例ID作为键
		local storageKey = data.weaponInstanceId or data.weaponName

		if data.weaponInstanceId then
			-- 使用实例ID保存，包含更多信息
			self.SavedWeaponAmmo[player.UserId][storageKey] = {
				weaponName = data.weaponName,
				savedAmmo = data.savedAmmo,
				timestamp = tick()
			}
			print("已保存武器弹药状态(实例ID): " .. player.Name .. ", " .. storageKey .. ", 弹药: " .. data.savedAmmo)
		else
			-- 向后兼容：使用武器名称保存
			self.SavedWeaponAmmo[player.UserId][storageKey] = data.savedAmmo
			print("已保存武器弹药状态(名称): " .. player.Name .. ", " .. storageKey .. ", 弹药: " .. data.savedAmmo)
		end

		-- 同时更新全局武器实例状态，确保状态一致性
		if data.weaponInstanceId then
			self:UpdateGlobalWeaponInstanceState(data.weaponInstanceId, {
				currentAmmo = data.savedAmmo,
				weaponName = data.weaponName,
				lastUpdated = tick()
			})
			print("同步更新全局武器实例状态: " .. data.weaponInstanceId .. ", 弹药: " .. data.savedAmmo)
		end
	else
		print("未保存武器弹药状态: savedAmmo为nil或负数")
	end
end

-- 处理换弹取消
function WeaponServer:HandleReloadCancelled(player, data)
	if not player or not data then return end

	print("处理换弹取消事件: " .. player.Name .. ", 武器: " .. (data.weaponName or "未知"))

	-- 获取玩家当前武器数据
	local weaponData = playerWeaponData[player]
	if not weaponData then
		print("找不到玩家武器数据，无法取消换弹")
		return
	end

	-- 取消换弹任务
	local taskCancelled = self:CancelReloadTask(player, weaponData.Id)

	-- 重置换弹状态
	if weaponData.isReloading then
		weaponData.isReloading = false
		print("服务端已取消玩家 " .. player.Name .. " 的换弹状态" .. (taskCancelled and "（任务已取消）" or ""))

		-- 通知客户端取消换弹
		NotifyService.FireClient("ReloadCancelled", player, {})
	elseif taskCancelled then
		print("服务端已取消玩家 " .. player.Name .. " 的换弹任务，但武器状态已重置")
	end
end

-- 清理过期的保存状态（定期调用）
function WeaponServer:CleanupExpiredSavedStates()
	if not self.SavedWeaponAmmo then return end

	local currentTime = tick()
	local expireTime = 300 -- 5分钟过期

	for userId, userStates in pairs(self.SavedWeaponAmmo) do
		for stateKey, stateData in pairs(userStates) do
			-- 检查是否是新格式的状态数据（包含timestamp）
			if type(stateData) == "table" and stateData.timestamp then
				if currentTime - stateData.timestamp > expireTime then
					userStates[stateKey] = nil
					print("清理过期的武器状态: " .. stateKey)
				end
			elseif type(stateData) == "number" then
				-- 旧格式的状态数据，保留一段时间后清理
				-- 这里可以设置一个更短的过期时间，或者直接保留
			end
		end

		-- 如果用户的所有状态都被清理了，删除用户条目
		if next(userStates) == nil then
			self.SavedWeaponAmmo[userId] = nil
		end
	end
end

-- 初始化定期清理任务
function WeaponServer:InitializeCleanupTask()
	-- 每60秒清理一次过期状态
	spawn(function()
		while true do
			wait(60)
			self:CleanupExpiredSavedStates()
		end
	end)
end

-- 事件已在ProtocolManager中注册

return WeaponServer