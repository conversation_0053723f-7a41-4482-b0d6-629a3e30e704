--!strict
-- OccupationConfig 数据解析工具
-- 用于解析职业配置中的 CarryingItem 字符串格式

local OccupationDataParser = {}

-- 解析单个物品字符串 "数量_物品类型_物品ID"
-- @param itemStr: string - 物品字符串，格式如 "2_7_10026"
-- @return table - 解析后的物品数据 {quantity: number, itemType: number, id: number}
local function parseItemString(itemStr)
    if not itemStr or itemStr == "" then
        warn("OccupationDataParser: 空的物品字符串")
        return nil
    end
    
    local parts = string.split(itemStr, "_")
    if #parts ~= 3 then
        warn("OccupationDataParser: 物品字符串格式错误: " .. itemStr)
        return nil
    end
    
    local quantity = tonumber(parts[1])
    local itemType = tonumber(parts[2])
    local id = tonumber(parts[3])
    
    if not quantity or not itemType or not id then
        warn("OccupationDataParser: 无法解析物品数据: " .. itemStr)
        return nil
    end
    
    return {
        quantity = quantity,
        itemType = itemType,
        id = id
    }
end

-- 解析职业携带物品字符串
-- @param carryingItemStr: string - 携带物品字符串，格式如 "2_7_10026;50_6_10029;1_10_10051"
-- @return table - 解析后的物品数组
function OccupationDataParser.parseCarryingItems(carryingItemStr)
    if not carryingItemStr or carryingItemStr == "" then
        warn("OccupationDataParser: 空的携带物品字符串")
        return {}
    end
    
    local items = {}
    local itemStrings = string.split(carryingItemStr, ";")
    
    for _, itemStr in ipairs(itemStrings) do
        local item = parseItemString(itemStr)
        if item then
            table.insert(items, item)
        end
    end
    
    return items
end

-- 根据职业ID获取携带物品数据
-- @param occupationId: number - 职业ID
-- @param occupationConfig: table - 职业配置表
-- @return table - 解析后的物品数组
function OccupationDataParser.getItemsByOccupationId(occupationId, occupationConfig)
    if not occupationId or not occupationConfig then
        warn("OccupationDataParser: 缺少必要参数")
        return {}
    end
    
    -- 查找对应的职业配置
    for _, config in ipairs(occupationConfig) do
        if config.Id == occupationId then
            if config.CarryingItem then
                return OccupationDataParser.parseCarryingItems(config.CarryingItem)
            else
                warn("OccupationDataParser: 职业ID " .. occupationId .. " 没有携带物品配置")
                return {}
            end
        end
    end
    
    warn("OccupationDataParser: 未找到职业ID " .. occupationId .. " 的配置")
    return {}
end

-- 验证物品数据格式
-- @param items: table - 物品数组
-- @return boolean - 是否有效
function OccupationDataParser.validateItems(items)
    if not items or type(items) ~= "table" then
        return false
    end
    
    for _, item in ipairs(items) do
        if type(item) ~= "table" or 
           not item.quantity or not item.itemType or not item.id or
           type(item.quantity) ~= "number" or 
           type(item.itemType) ~= "number" or 
           type(item.id) ~= "number" then
            return false
        end
    end
    
    return true
end

-- 调试输出物品数据
-- @param items: table - 物品数组
-- @param playerName: string - 玩家名称（用于调试）
function OccupationDataParser.debugPrintItems(items, playerName)
    if not items or #items == 0 then
        print("OccupationDataParser: " .. (playerName or "Unknown") .. " 没有携带物品")
        return
    end
    
    print("OccupationDataParser: " .. (playerName or "Unknown") .. " 的携带物品:")
    for i, item in ipairs(items) do
        print(string.format("  [%d] ID:%d, 数量:%d, 类型:%d", 
            i, item.id, item.quantity, item.itemType))
    end
end

return OccupationDataParser
