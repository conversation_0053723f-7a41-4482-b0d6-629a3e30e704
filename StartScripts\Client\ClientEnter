local ReplicatedStorage = game:GetService("ReplicatedStorage")
local player = game:GetService("Players")
local run = game:GetService("RunService")

local notifyManager = require(ReplicatedStorage.Scripts.Share.Manager.NotifyManager)
local protocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
--local TouchController=require(ReplicatedStorage.Scripts.Client.Controller.TouchController)
local LoadUIController=require(ReplicatedStorage.Scripts.Client.Controller.LoadUIController)
local ProfessionSwitch = require(ReplicatedStorage.Scripts.Client.Controller.ProfessionSwitch)
local SpecialCurrency = require(ReplicatedStorage.Scripts.Client.Controller.SpecialCurrencyController)
local ClientEnter = {}

function ClientEnter.Init()
	print("客户端初始化")

	protocolManager.InitClientProtocol()

	notifyManager.RegisterClientEvent("LoadUI",function(msg)
		LoadUIController:Init()
		
	end)
	notifyManager.RegisterClientEvent("LoadLeftUI",function()
		LoadUIController:LoadLeftBtn()
	end)
	
	
	notifyManager.RegisterClientEvent("UpdataUI",function(data)
		LoadUIController:UpdataUI(data)
	end)
	notifyManager.RegisterClientEvent("StartTime",function()
		print("start")
		LoadUIController:startCountdown(10,function()
			protocolManager.SendMessage("TimeOver")
		end)
	end)
	notifyManager.RegisterClientEvent("StopTime",function()
		print("1")
		LoadUIController:stopCountdown()
	end)
	notifyManager.RegisterClientEvent("IsLoad",function(data)
		LoadUIController.IsLoad=data
		
	end)
	ProfessionSwitch.InitClient()
	SpecialCurrency.InitClient()
	print("客户端初始化完成")
end

return ClientEnter