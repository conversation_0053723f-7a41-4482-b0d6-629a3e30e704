local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local Players = game:GetService("Players")
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local WeaponConfig = require(ReplicatedStorage.Scripts.Config.WeaponConfig)
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local WeaponController = {}

-- 按键绑定配置
local KEY_BINDINGS = {
	SKILL_2 = Enum.KeyCode.Q, -- 第二技能按键绑定为Q
	SKILL_3 = Enum.KeyCode.E, -- 第三技能按键绑定为E
	RELOAD = Enum.KeyCode.R   -- 换弹按键绑定为R
}

-- 显示技能冷却UI（可以自行实现）
local function showSkillCooldown(skillIndex, currentTime, maxTime)
	-- 这里可以实现技能冷却的UI显示
	print("技能" .. skillIndex .. "冷却中: " .. currentTime .. "/" .. maxTime)
end

-- 检查玩家是否处于濒死状态的辅助函数
local function isPlayerDowned()
	local player = Players.LocalPlayer
	if not player then return false end

	-- 检查玩家的IsDowned属性
	if player:GetAttribute("IsDowned") then
		return true
	end

	-- 检查角色的IsDowned属性
	local character = player.Character
	if character and character:GetAttribute("IsDowned") then
		return true
	end

	-- 检查DisablePickup属性
	if player:GetAttribute("DisablePickup") then
		return true
	end

	return false
end

function WeaponController.Init()
	print("武器控制器开始初始化")
	local attacking = false

	-- 注册服务端通知事件 - 对于UI反馈
	NotifyService.RegisterClientEvent("WeaponSkillCooldown", function(data)
		if data.skillIndex and data.remainingTime then
			showSkillCooldown(data.skillIndex, data.remainingTime, data.cooldownTime)
		end
	end)

	-- 处理鼠标按下事件
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end

		if input.UserInputType == Enum.UserInputType.MouseButton1 then
			-- 主要攻击按钮
			if WeaponClient.IsWeaponEquipped then
				-- 检查武器类型
				if WeaponClient.WeaponData.WeaponType == 1 then -- 近战武器
					if not attacking then
						attacking = true
						WeaponClient:Attack()
						task.delay(0.2, function() -- 短暂冷却，避免连点
							attacking = false
						end)
					end
				elseif WeaponClient.WeaponData.WeaponType == 2 then -- 远程武器
					-- 开始射击
					WeaponClient:StartShooting()
				end
			end
		elseif input.KeyCode == KEY_BINDINGS.SKILL_2 then
			-- 释放第二技能（仅近战武器）
			if WeaponClient.IsWeaponEquipped and WeaponClient.WeaponData.WeaponType == 1 then
				if WeaponClient:IsSkillOnCooldown(2) then
					-- 技能冷却中，可以添加提示或UI反馈
					local skillData = WeaponClient.Skills[2]
					if skillData then
						local now = tick()
						local lastUse = WeaponClient.SkillCooldowns[2] or 0
						local remaining = skillData.Cooldown - (now - lastUse)
						if remaining > 0 then
							showSkillCooldown(2, remaining, skillData.Cooldown)
						end
					end
				else
					WeaponClient:UseSecondSkill()
				end
			end
		elseif input.KeyCode == KEY_BINDINGS.SKILL_3 then
			-- 释放第三技能（仅近战武器）
			if WeaponClient.IsWeaponEquipped and WeaponClient.WeaponData.WeaponType == 1 then
				if WeaponClient:IsSkillOnCooldown(3) then
					-- 技能冷却中，可以添加提示或UI反馈
					local skillData = WeaponClient.Skills[3]
					if skillData then
						local now = tick()
						local lastUse = WeaponClient.SkillCooldowns[3] or 0
						local remaining = skillData.Cooldown - (now - lastUse)
						if remaining > 0 then
							showSkillCooldown(3, remaining, skillData.Cooldown)
						end
					end
				else
					WeaponClient:UseThirdSkill()
				end
			end
		elseif input.KeyCode == KEY_BINDINGS.RELOAD then
			-- 远程武器换弹
			if WeaponClient.IsWeaponEquipped and WeaponClient.WeaponData.WeaponType == 2 then
				print("按R键触发换弹, 武器ID: " .. WeaponClient.WeaponData.Id)
				WeaponClient:Reload()
			end
		end
	end)

	-- 处理鼠标松开事件
	UserInputService.InputEnded:Connect(function(input, gameProcessed)
		if gameProcessed then return end

		if input.UserInputType == Enum.UserInputType.MouseButton1 then
			-- 如果是远程武器，停止射击
			if WeaponClient.IsWeaponEquipped and WeaponClient.WeaponData.WeaponType == 2 then
				WeaponClient:StopShooting()
			end
		end
	end)

	print("武器控制器初始化完成")
end

return WeaponController