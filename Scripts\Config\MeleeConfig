--!strict
export type MeleeConfig = {
  Id: number,
  AttackValue: number,
  AttackRange: Vector3,
  FirstAttackSkill: number,
  SecondAttackSkill: number,
  ThirdAttackSkill: number
}

local MeleeConfig: {MeleeConfig} = {
	{Id=10031, AttackValue=10, AttackRange=Vector3.new(10,3,5), FirstAttackSkill=10001, SecondAttackSkill=10002, ThirdAttackSkill=10003},
	{Id=10032, AttackValue=20, AttackRange=Vector3.new(6,5,7), FirstAttackSkill=10004, SecondAttackSkill=10005, ThirdAttackSkill=10006}
}

return MeleeConfig
