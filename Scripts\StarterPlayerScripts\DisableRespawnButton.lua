--[[
直接禁用系统重生按钮
简单有效的解决方案：完全禁用重生按钮功能
]]

local Players = game:GetService("Players")
local StarterGui = game:GetService("StarterGui")

local DisableRespawnButton = {}

-- 状态变量
local player = Players.LocalPlayer
local disableActive = false

-- 初始化禁用器
function DisableRespawnButton:Initialize()
    print("🚫 初始化重生按钮禁用器...")
    
    -- 等待玩家完全加载
    if not player.Character then
        player.CharacterAdded:Wait()
    end
    
    -- 延迟启动，确保游戏完全加载
    spawn(function()
        wait(3) -- 等待3秒确保所有系统加载完成
        self:DisableRespawnButton()
    end)
    
    -- 监听角色重生，重新禁用按钮
    player.CharacterAdded:Connect(function(character)
        spawn(function()
            wait(1) -- 等待角色完全加载
            self:DisableRespawnButton()
        end)
    end)
    
    print("✅ 重生按钮禁用器初始化完成")
end

-- 禁用重生按钮
function DisableRespawnButton:DisableRespawnButton()
    if disableActive then
        return
    end
    
    disableActive = true
    print("🚫 正在禁用重生按钮...")
    
    -- 方法1：设置重生按钮回调为空函数（阻止重生）
    local success1, error1 = pcall(function()
        StarterGui:SetCore("ResetButtonCallback", function()
            print("🚫 重生按钮已被禁用")
            -- 什么都不做，阻止重生
        end)
        print("✅ 方法1成功：重生按钮回调已设置为空")
    end)
    
    -- 方法2：尝试完全隐藏重生按钮
    local success2, error2 = pcall(function()
        StarterGui:SetCore("ResetButtonCallback", false)
        print("✅ 方法2成功：重生按钮已隐藏")
    end)
    
    -- 方法3：设置CoreGui状态（备用方案）
    local success3, error3 = pcall(function()
        StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.Backpack, true) -- 保持背包
        StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.PlayerList, true) -- 保持玩家列表
        StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.Chat, true) -- 保持聊天
        -- 不禁用整个菜单，只是让重生按钮无效
        print("✅ 方法3成功：CoreGui状态已设置")
    end)
    
    if success1 or success2 or success3 then
        print("✅ 重生按钮已成功禁用")
        self:CreateDisabledNotification()
    else
        warn("❌ 禁用重生按钮失败:")
        if error1 then warn("  方法1错误: " .. tostring(error1)) end
        if error2 then warn("  方法2错误: " .. tostring(error2)) end
        if error3 then warn("  方法3错误: " .. tostring(error3)) end
        
        -- 备用方案：延迟重试
        spawn(function()
            wait(2)
            disableActive = false
            self:DisableRespawnButton()
        end)
    end
end

-- 创建禁用通知UI
function DisableRespawnButton:CreateDisabledNotification()
    spawn(function()
        wait(1)
        
        local screenGui = Instance.new("ScreenGui")
        screenGui.Name = "RespawnDisabledNotification"
        screenGui.ResetOnSpawn = false
        screenGui.Parent = player.PlayerGui
        
        local notificationFrame = Instance.new("Frame")
        notificationFrame.Size = UDim2.new(0, 300, 0, 60)
        notificationFrame.Position = UDim2.new(1, -310, 0, 10)
        notificationFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
        notificationFrame.BackgroundTransparency = 0.2
        notificationFrame.BorderSizePixel = 0
        notificationFrame.Parent = screenGui
        
        local corner = Instance.new("UICorner")
        corner.CornerRadius = UDim.new(0, 8)
        corner.Parent = notificationFrame
        
        local titleLabel = Instance.new("TextLabel")
        titleLabel.Size = UDim2.new(1, -10, 0, 25)
        titleLabel.Position = UDim2.new(0, 5, 0, 5)
        titleLabel.BackgroundTransparency = 1
        titleLabel.Text = "🚫 重生按钮已禁用"
        titleLabel.TextColor3 = Color3.new(1, 0.3, 0.3)
        titleLabel.TextSize = 16
        titleLabel.Font = Enum.Font.SourceSansBold
        titleLabel.TextXAlignment = Enum.TextXAlignment.Left
        titleLabel.Parent = notificationFrame
        
        local descLabel = Instance.new("TextLabel")
        descLabel.Size = UDim2.new(1, -10, 0, 25)
        descLabel.Position = UDim2.new(0, 5, 0, 30)
        descLabel.BackgroundTransparency = 1
        descLabel.Text = "使用 /downed 命令进入濒死状态"
        descLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
        descLabel.TextSize = 12
        descLabel.Font = Enum.Font.SourceSans
        descLabel.TextXAlignment = Enum.TextXAlignment.Left
        descLabel.Parent = notificationFrame
        
        -- 5秒后自动隐藏通知
        spawn(function()
            wait(5)
            if screenGui and screenGui.Parent then
                screenGui:Destroy()
            end
        end)
        
        print("✅ 禁用通知UI已创建")
    end)
end

-- 检查禁用状态
function DisableRespawnButton:IsDisabled()
    return disableActive
end

-- 重新启用重生按钮（如果需要）
function DisableRespawnButton:EnableRespawnButton()
    if not disableActive then
        return
    end
    
    disableActive = false
    
    -- 尝试恢复默认重生按钮行为
    local success, error = pcall(function()
        StarterGui:SetCore("ResetButtonCallback", true)
        print("✅ 已恢复默认重生按钮行为")
    end)
    
    if not success then
        warn("⚠️ 无法恢复默认重生按钮: " .. tostring(error))
    end
    
    print("🔄 重生按钮禁用器已停止")
end

-- 创建状态显示UI
function DisableRespawnButton:CreateStatusUI()
    spawn(function()
        wait(1)
        
        local screenGui = Instance.new("ScreenGui")
        screenGui.Name = "RespawnDisabledStatus"
        screenGui.ResetOnSpawn = false
        screenGui.Parent = player.PlayerGui
        
        local statusLabel = Instance.new("TextLabel")
        statusLabel.Size = UDim2.new(0, 200, 0, 25)
        statusLabel.Position = UDim2.new(1, -210, 0, 80)
        statusLabel.BackgroundColor3 = Color3.new(0, 0, 0)
        statusLabel.BackgroundTransparency = 0.3
        statusLabel.BorderSizePixel = 0
        statusLabel.Text = "重生按钮: 🚫 已禁用"
        statusLabel.TextColor3 = Color3.new(1, 0.3, 0.3)
        statusLabel.TextSize = 12
        statusLabel.Font = Enum.Font.SourceSansBold
        statusLabel.Parent = screenGui
        
        local corner = Instance.new("UICorner")
        corner.CornerRadius = UDim.new(0, 5)
        corner.Parent = statusLabel
        
        -- 更新状态显示
        spawn(function()
            while screenGui.Parent do
                local status = self:IsDisabled() and "🚫 已禁用" or "✅ 已启用"
                statusLabel.Text = "重生按钮: " .. status
                statusLabel.TextColor3 = self:IsDisabled() and Color3.new(1, 0.3, 0.3) or Color3.new(0.3, 1, 0.3)
                wait(1)
            end
        end)
        
        print("✅ 状态显示UI已创建")
    end)
end

-- 自动初始化
DisableRespawnButton:Initialize()

-- 创建状态显示UI（可选）
DisableRespawnButton:CreateStatusUI()

print("🚫 重生按钮禁用脚本已加载")
print("📋 功能说明:")
print("  - 重生按钮已被完全禁用")
print("  - 点击重生按钮不会有任何效果")
print("  - 使用 /downed 命令可以进入濒死状态")
print("  - 濒死状态下可以使用自我复活功能")

return DisableRespawnButton
