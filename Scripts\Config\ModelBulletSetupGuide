-- Model类型子弹设置指南
-- 如何在Roblox Studio中创建和配置Model类型的子弹

--[[
=== Model类型子弹设置步骤 ===

1. 在ReplicatedStorage中创建子弹模型结构：
   ReplicatedStorage/
   └── Model/
       └── Equip/
           └── Bullet/
               ├── Bullet_01 (BasePart类型)
               ├── Bullet_02 (BasePart类型)
               └── Fireball (Model类型) ← 新增

2. 创建Fireball Model：
   - 在Bullet文件夹中创建一个Model，命名为"Fireball"
   - 在Fireball Model中添加以下部件：

   Fireball (Model)
   ├── Core (Part) ← 设为PrimaryPart
   │   ├── Fire (Fire效果)
   │   ├── PointLight (光效)
   │   └── ParticleEmitter (粒子效果)
   ├── Flame1 (Part)
   ├── Flame2 (Part)
   └── Flame3 (Part)

3. 设置Core部件属性：
   - Material: Neon
   - BrickColor: Bright red
   - Shape: Ball
   - Size: Vector3(2, 2, 2)
   - CanCollide: false
   - Anchored: false

4. 设置Fire效果：
   - Size: 5
   - Heat: 10
   - Color: Color3(1, 0.5, 0)
   - SecondaryColor: Color3(1, 0, 0)

5. 设置PointLight：
   - Brightness: 2
   - Color: Color3(1, 0.5, 0)
   - Range: 10

6. 设置ParticleEmitter：
   - Texture: rbxasset://textures/particles/fire_main.dds
   - Lifetime: NumberRange(0.3, 1.0)
   - Rate: 50
   - SpreadAngle: Vector2(45, 45)

7. 设置Flame部件：
   - Material: ForceField
   - Transparency: 0.5
   - BrickColor: Bright orange
   - CanCollide: false
   - 围绕Core部件排列

8. 设置Model的PrimaryPart：
   - 选择Fireball Model
   - 在Properties中设置PrimaryPart为Core

=== 配置文件更新 ===

在RemoteWeaponConfig中添加Fireball配置：
{
    Id=10053, 
    BallisticId=10031, 
    WeaponType=5, 
    BallisticModel="Fireball", 
    BallisticType="Model",  ← 关键：指定为Model类型
    BallisticSpeed=300, 
    ReloadTime=1.5, 
    SwapSoundEffect="rbxassetid://138084889", 
    ShootSoundEffect="rbxassetid://1905367471", 
    Range=120, 
    Damage=75, 
    AmmoCapacity=20, 
    ShootCD=1.2
}

=== 测试步骤 ===

1. 运行ModelBulletTest脚本：
   local ModelBulletTest = require(ReplicatedStorage.Scripts.Test.ModelBulletTest)
   ModelBulletTest:QuickTest()

2. 在游戏中装备支持Fireball的武器

3. 发射子弹，观察Fireball的飞行效果

=== 预期效果 ===

- Fireball作为完整的Model飞行
- 保持火焰效果和光效
- 正确的碰撞检测
- 命中目标时正确销毁

=== 故障排除 ===

如果Fireball不显示：
1. 检查ReplicatedStorage路径是否正确
2. 确认Model的PrimaryPart已设置
3. 检查RemoteWeaponConfig中的BallisticType字段
4. 查看控制台错误信息

如果Fireball不移动：
1. 确认PrimaryPart不是Anchored
2. 检查物理属性设置
3. 确认AssemblyLinearVelocity正确应用

如果碰撞检测不工作：
1. 检查Model中所有Part的CanCollide设置
2. 确认Touched事件正确连接
3. 检查碰撞过滤逻辑

=== 扩展功能 ===

可以为Model类型子弹添加：
1. 轨迹特效（Trail）
2. 音效（Sound）
3. 爆炸效果（Explosion）
4. 自定义动画（TweenService）
5. 粒子系统（ParticleEmitter）

=== 性能优化 ===

1. 限制同时存在的Model子弹数量
2. 使用对象池重用Model
3. 及时清理过期的子弹
4. 优化粒子效果的数量和质量

--]]

-- 示例：创建Fireball Model的脚本
local function createFireballModel()
    local fireball = Instance.new("Model")
    fireball.Name = "Fireball"
    
    -- 创建核心部件
    local core = Instance.new("Part")
    core.Name = "Core"
    core.Material = Enum.Material.Neon
    core.BrickColor = BrickColor.new("Bright red")
    core.Shape = Enum.PartType.Ball
    core.Size = Vector3.new(2, 2, 2)
    core.CanCollide = false
    core.Anchored = false
    core.Parent = fireball
    
    -- 设置为PrimaryPart
    fireball.PrimaryPart = core
    
    -- 添加火焰效果
    local fire = Instance.new("Fire")
    fire.Size = 5
    fire.Heat = 10
    fire.Color = Color3.new(1, 0.5, 0)
    fire.SecondaryColor = Color3.new(1, 0, 0)
    fire.Parent = core
    
    -- 添加光效
    local light = Instance.new("PointLight")
    light.Brightness = 2
    light.Color = Color3.new(1, 0.5, 0)
    light.Range = 10
    light.Parent = core
    
    -- 添加粒子效果
    local particles = Instance.new("ParticleEmitter")
    particles.Texture = "rbxasset://textures/particles/fire_main.dds"
    particles.Lifetime = NumberRange.new(0.3, 1.0)
    particles.Rate = 50
    particles.SpreadAngle = Vector2.new(45, 45)
    particles.Parent = core
    
    -- 创建火焰部件
    for i = 1, 3 do
        local flame = Instance.new("Part")
        flame.Name = "Flame" .. i
        flame.Material = Enum.Material.ForceField
        flame.Transparency = 0.5
        flame.BrickColor = BrickColor.new("Bright orange")
        flame.Size = Vector3.new(1, 1, 1)
        flame.CanCollide = false
        flame.Anchored = false
        
        -- 围绕核心排列
        local angle = (i - 1) * (math.pi * 2 / 3)
        local offset = Vector3.new(math.cos(angle) * 1.5, 0, math.sin(angle) * 1.5)
        flame.CFrame = core.CFrame + offset
        
        flame.Parent = fireball
        
        -- 将火焰部件焊接到核心
        local weld = Instance.new("WeldConstraint")
        weld.Part0 = core
        weld.Part1 = flame
        weld.Parent = core
    end
    
    return fireball
end

-- 使用示例：
-- local fireball = createFireballModel()
-- fireball.Parent = ReplicatedStorage.Model.Equip.Bullet

return {
    createFireballModel = createFireballModel
}
