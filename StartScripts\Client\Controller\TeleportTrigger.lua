--!strict
-- 传送触发器
-- 用于在初始场景中触发传送到游戏场景

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local ProtocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local NotifyManager = require(ReplicatedStorage.Scripts.Share.Manager.NotifyManager)

local TeleportTrigger = {}

-- 创建传送按钮UI
local function createTeleportUI()
    local player = Players.LocalPlayer
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- 检查是否已存在UI
    local existingUI = playerGui:FindFirstChild("TeleportUI")
    if existingUI then
        existingUI:Destroy()
    end
    
    -- 创建ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "TeleportUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- 创建主框架
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 300, 0, 150)
    mainFrame.Position = UDim2.new(0.5, -150, 0.8, -75)
    mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    mainFrame.BorderSizePixel = 0
    mainFrame.Parent = screenGui
    
    -- 添加圆角
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 10)
    corner.Parent = mainFrame
    
    -- 创建标题
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "TitleLabel"
    titleLabel.Size = UDim2.new(1, 0, 0, 40)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "准备进入游戏"
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame
    
    -- 创建状态标签
    local statusLabel = Instance.new("TextLabel")
    statusLabel.Name = "StatusLabel"
    statusLabel.Size = UDim2.new(1, -20, 0, 30)
    statusLabel.Position = UDim2.new(0, 10, 0, 45)
    statusLabel.BackgroundTransparency = 1
    statusLabel.Text = "请先选择职业"
    statusLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    statusLabel.TextScaled = true
    statusLabel.Font = Enum.Font.SourceSans
    statusLabel.Parent = mainFrame
    
    -- 创建传送按钮
    local teleportButton = Instance.new("TextButton")
    teleportButton.Name = "TeleportButton"
    teleportButton.Size = UDim2.new(0, 200, 0, 40)
    teleportButton.Position = UDim2.new(0.5, -100, 1, -50)
    teleportButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
    teleportButton.Text = "进入游戏"
    teleportButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    teleportButton.TextScaled = true
    teleportButton.Font = Enum.Font.SourceSansBold
    teleportButton.Enabled = false
    teleportButton.Parent = mainFrame
    
    -- 按钮圆角
    local buttonCorner = Instance.new("UICorner")
    buttonCorner.CornerRadius = UDim.new(0, 5)
    buttonCorner.Parent = teleportButton
    
    -- 按钮点击事件
    teleportButton.Activated:Connect(function()
        print("玩家点击传送按钮")
        ProtocolManager.SendMessage("TeleportToGame", {})
    end)
    
    return {
        screenGui = screenGui,
        statusLabel = statusLabel,
        teleportButton = teleportButton
    }
end

-- 更新UI状态
local function updateUIStatus(ui, hasProfession, profession)
    if not ui then return end
    
    if hasProfession and profession then
        ui.statusLabel.Text = "当前职业: " .. profession
        ui.statusLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
        ui.teleportButton.Enabled = true
        ui.teleportButton.BackgroundColor3 = Color3.fromRGB(50, 150, 50)
        ui.teleportButton.Text = "进入游戏"
    else
        ui.statusLabel.Text = "请先选择职业"
        ui.statusLabel.TextColor3 = Color3.fromRGB(255, 100, 100)
        ui.teleportButton.Enabled = false
        ui.teleportButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
        ui.teleportButton.Text = "需要选择职业"
    end
end

-- 检查玩家是否有职业
local function checkPlayerProfession()
    -- 这里应该从某个地方获取玩家的职业状态
    -- 暂时返回false，实际使用时需要连接到职业系统
    return false, nil
end

-- 初始化传送触发器
function TeleportTrigger.init()
    print("TeleportTrigger: 初始化传送触发器")
    
    -- 创建UI
    local ui = createTeleportUI()
    
    -- 初始状态检查
    local hasProfession, profession = checkPlayerProfession()
    updateUIStatus(ui, hasProfession, profession)
    
    -- 监听职业选择事件
    NotifyManager.RegisterClientEvent("RecordProfession", function(data)
        if data and data.profession then
            print("TeleportTrigger: 检测到职业选择: " .. data.profession)
            updateUIStatus(ui, true, data.profession)
        end
    end)
    
    -- 监听传送错误事件
    NotifyManager.RegisterClientEvent("TeleportError", function(data)
        if data and data.message then
            print("TeleportTrigger: 传送错误: " .. data.message)
            
            -- 显示错误消息
            ui.statusLabel.Text = data.message
            ui.statusLabel.TextColor3 = Color3.fromRGB(255, 100, 100)
            
            -- 3秒后恢复状态
            task.wait(3)
            local hasProfession, profession = checkPlayerProfession()
            updateUIStatus(ui, hasProfession, profession)
        end
    end)
    
    print("TeleportTrigger: 传送触发器初始化完成")
end

-- 手动触发传送（用于测试）
function TeleportTrigger.manualTeleport()
    print("TeleportTrigger: 手动触发传送")
    ProtocolManager.SendMessage("TeleportToGame", {})
end

-- 显示/隐藏传送UI
function TeleportTrigger.toggleUI(visible)
    local player = Players.LocalPlayer
    local playerGui = player:WaitForChild("PlayerGui")
    local teleportUI = playerGui:FindFirstChild("TeleportUI")
    
    if teleportUI then
        teleportUI.Enabled = visible
    end
end

-- 设置测试命令
function TeleportTrigger.setupTestCommands()
    local player = Players.LocalPlayer
    
    player.Chatted:Connect(function(message)
        if message == "/teleport_ui" then
            TeleportTrigger.init()
            print("重新创建传送UI")
        elseif message == "/manual_teleport" then
            TeleportTrigger.manualTeleport()
        elseif message == "/hide_teleport_ui" then
            TeleportTrigger.toggleUI(false)
            print("隐藏传送UI")
        elseif message == "/show_teleport_ui" then
            TeleportTrigger.toggleUI(true)
            print("显示传送UI")
        end
    end)
    
    print("TeleportTrigger: 测试命令设置完成")
    print("可用命令: /teleport_ui, /manual_teleport, /hide_teleport_ui, /show_teleport_ui")
end

return TeleportTrigger
