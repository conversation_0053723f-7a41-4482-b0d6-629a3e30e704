local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local ProfessionConfig = require(ReplicatedStorage.Scripts.Config.ProfessionConfig)
local OccupationConfig = require(ReplicatedStorage.Scripts.Config.OccupationConfig)
local OccupationDataParser = require(ReplicatedStorage.Scripts.Server.Utils.OccupationDataParser)
local NotifyManager 	= require(ReplicatedStorage.Scripts.Share.Manager.NotifyManager)
local ProfessionManager = {}
ProfessionManager.playerProfession = {}		-- 记录玩家当前职业
ProfessionManager.playerProfessionState = {} -- 记录玩家当前职业数据ID

ProfessionManager.playerHasProfessions = {} -- 记录玩家已经解锁的职业
local ConfigById = {}
for _, config in ipairs(ProfessionConfig) do
	ConfigById[config.Id] = config
end

-- 更新角色属性（优化后：直接通过 Id 查配置）
local function UpdateState(humanoid, Id)
	local config = ConfigById[Id]
	if config then
		humanoid.WalkSpeed = config.MoveSpeed
		humanoid.MaxHealth = config.Health
		humanoid.Health = humanoid.MaxHealth
	else
		warn("未找到 Id 为 "..Id.." 的职业配置")
	end
end
-- 检查玩家当前职业
local function isUsingProfession(userId, targetProfession)
	local userProfessions = ProfessionManager.playerProfession[userId]
	if not userProfessions then return false end

	if userProfessions == targetProfession then
		return true
	end
	return false
end
-- 检查玩家是否已解锁目标职业
local function hasUnlockedProfession(userId, targetProfession)
	local unlocked = ProfessionManager.playerHasProfessions[userId]
	if not unlocked then return false end
	for _, pro in ipairs(unlocked) do
		if pro == targetProfession then
			return true
		end
	end
	return false
end
function ProfessionManager.changeModel(player, professionModel,Id,price,profession)
	--  基础参数校验
	if not player or not player.Character then
		warn("更换职业失败：玩家或角色不存在")
		return false
	end
	if not professionModel then
		warn("更换职业失败：职业模型不存在")
		return false
	end
	if not Id or not profession then
		warn("更换职业失败：缺少 Id 或 profession 参数")
		return false
	end

	local userId = player.UserId
	local oldChar = player.Character
	-- 检查是否处于该职业
	if isUsingProfession(userId, profession) then
		warn("更换职业失败：玩家正在使用该职业（"..profession.."）")
		return false
	end

	local specialCurrency = require(ReplicatedStorage.Scripts.Server.Manager.SpecialCurrencyManager)
	-- 处理价格：默认 price 为 0（免费解锁）
	if not price then
		warn("缺少价格")
		return false
	end
	-- 检查金币是否足够
	if not specialCurrency.HasEnoughCurrency(player, price) then
		local currency = specialCurrency.GetCurrency(player)
		warn("金币不足，无法购买 "..profession.."，当前金币："..currency.."，所需："..price)
		return false
	end

	-- 提前扣除金币
	if not specialCurrency.RemoveCurrency(player, price)  then
		warn("扣除金币失败，无法购买职业")
		return false
	end
	-- 创建淡入淡出效果
	local screenGui = Instance.new("ScreenGui")
	screenGui.IgnoreGuiInset = true
	screenGui.Parent = player.PlayerGui

	local frame = Instance.new("Frame")
	frame.BackgroundColor3 = Color3.new(0, 0, 0)
	frame.Size = UDim2.new(1, 0, 1, 0)
	frame.BackgroundTransparency = 1
	frame.Parent = screenGui

	-- 淡出效果
	local fadeOut = TweenService:Create(
		frame, 
		TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), 
		{BackgroundTransparency = 0}
	)
	fadeOut:Play()
	fadeOut.Completed:Wait()

	-- 复制职业模型
	local newChar = professionModel:Clone()
	newChar.Name = player.Name
	newChar.Parent = workspace

	-- 保持位置和状态
	local rootOld = oldChar:FindFirstChild("HumanoidRootPart")
	local rootNew = newChar:FindFirstChild("HumanoidRootPart")
	if rootOld and rootNew then
		rootNew.CFrame = rootOld.CFrame

		-- 保留动量
		if rootOld.AssemblyLinearVelocity.Magnitude > 0.1 then
			rootNew.AssemblyLinearVelocity = rootOld.AssemblyLinearVelocity
		end
	end

	-- 设置玩家的 Character 引用
	player.Character = newChar
	-- 若不存在初始化玩家已解锁职业列表
	if not ProfessionManager.playerHasProfessions[userId] then
		ProfessionManager.playerHasProfessions[userId] = {}
	end

	-- 若未解锁该职业，则添加到已解锁列表
	if not hasUnlockedProfession(userId, profession) then
		table.insert(ProfessionManager.playerHasProfessions[userId], profession)
		print("记录职业",profession)
		
	end
	-- 等待新角色完全加载
	local humanoidRootPart = newChar:WaitForChild("HumanoidRootPart")
	local humanoid = newChar:WaitForChild("Humanoid",5)
	if humanoid then
		UpdateState(humanoid, Id)
		-- 记录职业数据（存储为表结构，方便后续扩展）
		ProfessionManager.playerProfession[userId] = profession
		ProfessionManager.playerProfessionState[userId] = Id
		print("职业更新成功：玩家（"..userId.."）新增职业 "..profession)
	else
		warn("更换职业警告：新角色缺少 Humanoid，无法更新状态")
	end
	-- 淡入效果
	local fadeIn = TweenService:Create(
		frame, 
		TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.In), 
		{BackgroundTransparency = 1}
	)
	fadeIn:Play()

	-- 淡出完成后销毁UI和旧角色
	fadeIn.Completed:Wait()
	screenGui:Destroy()

	-- 清理旧角色
	if oldChar and oldChar.Parent then
		-- 先断开所有连接
		for _, child in ipairs(oldChar:GetDescendants()) do
			if child:IsA("BasePart") then
				child.Touched:DisconnectAll()
			end
		end

		-- 延迟销毁，确保网络同步完成
		task.delay(2, function()
			if oldChar and oldChar.Parent then
				oldChar:Destroy()
			end
		end)
	end
	-- 获取职业对应的物品数据
	local playerItems = OccupationDataParser.getItemsByOccupationId(Id, OccupationConfig)

	-- 存储玩家的物品数据到ProfessionManager中，供TeleportManager使用
	if not ProfessionManager.playerItems then
		ProfessionManager.playerItems = {}
	end
	ProfessionManager.playerItems[userId] = {
		items = playerItems,
		playerName = player.Name,
		profession = profession,
		professionId = Id
	}

	print("ProfessionManager: 为玩家 " .. player.Name .. " 生成物品数据，共 " .. #playerItems .. " 个物品")

	-- 确保角色完全载入再发送事件
	NotifyManager.FireClient("RecordProfession",player,{profession = profession})
end
function ProfessionManager.InitServer()
	Players.PlayerAdded:Connect(function(player)
		local userId = player.UserId
		-- 尝试从数据存储加载
		local dataStoreService = game:GetService("DataStoreService")
		local playerDataStore = dataStoreService:GetDataStore("PlayerData")

		local success, data = pcall(function()
			return playerDataStore:GetAsync(userId)
		end)

		if success and data and data.hasProfession ~= nil then
			ProfessionManager.playerHasProfessions[userId] = data.hasProfession
			NotifyManager.FireClient("HasProfession",player,{hasProfession = ProfessionManager.playerHasProfessions[userId]})
			print(player,"已经解锁的职业",ProfessionManager.playerHasProfessions[userId])
			return false
		else
			ProfessionManager.playerHasProfessions[userId] = {}
			NotifyManager.FireClient("HasProfession",player,{hasProfession = ProfessionManager.playerHasProfessions[userId]})
			print(player,"未解锁职业")
			return true 
		end
	end)
	
end
return ProfessionManager
