local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")
local Workspace = game:GetService("Workspace")
local ConfigManager = require(ReplicatedStorage.Scripts.Share.Services.ConfigManager)
--local BuildingGenerator = require(ReplicatedStorage.Scripts.Server.Manager.BuildingGenerator)
local EventEntity=require(ReplicatedStorage.Scripts.Server.Manager.EventsManager.EventEntity)
local EventsManager = {}


EventsManager.AllEvents={}

local function ParseEventId(eventId)
	-- 解析EventId字段(格式: "10000_1_1_50001")
	-- 格式说明: 事件概率_参数1_参数2_目标ID
	local parts = string.split(eventId, "_")
	if #parts < 4 then
		warn("无效的EventId格式:", eventId)
		return nil
	end

	return {
		chance = tonumber(parts[1]),
		minNum = tonumber(parts[2]),
		maxNum = tonumber(parts[3]),
		targetId = tonumber(parts[4])
	}
end

local function GetEventPosition(pos)
	local parts=string.split(pos,"_")
	if #parts<3 then 
		return nil
	end
	return Vector3.new(tonumber(parts[1]),tonumber(parts[2]),tonumber(parts[3]))
		
	
end


function EventsManager:Init()
	local alleventConfig=ConfigManager.GetConfig("RandomEventConfig")
	local alleventData=alleventConfig.originalData
	
	if not EventsManager.AllEvents then
		print("fail to get RandomEventConfig")
		return
	end
	
	for _,v in pairs(alleventData) do
		if v.EventId then
			
			--print(v.Coordinate)
			--print(v.EventId)
			--调用生成事件的方法 参数 eventInfo 包括： 概率  minNum maxNum 目标建筑id
			EventEntity:CreatEvent(v.EventId,v.Coordinate)
	end
	
	

end

end



return EventsManager
