local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local ProtocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)

local DeathBoxClient = {}

-- 用于存储所有已创建的死亡盒子
DeathBoxClient.ActiveDeathBoxes = {}

-- 初始化方法
function DeathBoxClient:Initialize()
	print("DeathBoxClient 开始初始化")

	-- 注册死亡盒子相关事件
	self:RegisterEvents()

	print("DeathBoxClient 初始化完成")
end

-- 注册事件
function DeathBoxClient:RegisterEvents()
	-- 监听死亡盒子创建事件
	NotifyService.RegisterClientEvent("DeathBoxCreated", function(data)
		self:OnDeathBoxCreated(data)
	end)

	-- 监听死亡盒子移除事件
	NotifyService.RegisterClientEvent("DeathBoxRemoved", function(data)
		self:OnDeathBoxRemoved(data)
	end)
end

-- 处理死亡盒子创建
function DeathBoxClient:OnDeathBoxCreated(data)
	if not data or not data.boxId then return end

	print("收到死亡盒子创建通知: " .. data.boxId)

	-- 存储死亡盒子信息到本地列表
	self.ActiveDeathBoxes[data.boxId] = data

	-- 根据需要，可以在这里添加UI指示器或其他客户端表现
	-- 例如，在地图上添加标记指向死亡盒子位置
end

-- 处理死亡盒子移除
function DeathBoxClient:OnDeathBoxRemoved(data)
	if not data or not data.boxId then return end

	print("收到死亡盒子移除通知: " .. data.boxId)

	-- 从本地列表中移除
	self.ActiveDeathBoxes[data.boxId] = nil

	-- 移除相关的UI指示器或其他客户端表现
end

-- 向服务器请求拾取死亡盒子（如果需要）
function DeathBoxClient:RequestPickupDeathBox(boxId)
	-- 使用ProtocolManager发送拾取请求到服务器
	ProtocolManager.SendMessage("RequestPickupDeathBox", {boxId = boxId})
end

return DeathBoxClient 