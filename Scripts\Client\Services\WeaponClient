local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local SoundService = game:GetService("SoundService")
local WeaponConfig = require(ReplicatedStorage.Scripts.Config.WeaponConfig)
local SkillConfig = require(ReplicatedStorage.Scripts.Config.SkillConfig)
local MeleeConfig = require(ReplicatedStorage.Scripts.Config.MeleeConfig)
local RemoteWeaponConfig = require(ReplicatedStorage.Scripts.Config.RemoteWeaponConfig)
local ProtocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local WeaponClient = {}

WeaponClient.CurrentWeapon = nil
WeaponClient.animator = nil
WeaponClient.IsWeaponEquipped = false
WeaponClient.ComboIndex = 1
WeaponClient.LastAttackTime = 0
WeaponClient.CurrentAnimTrack = nil

-- 武器和技能配置数据
WeaponClient.WeaponData = nil
WeaponClient.MeleeData = nil
WeaponClient.RemoteData = nil
WeaponClient.Skills = {}


-- 技能冷却状态
WeaponClient.SkillCooldowns = {
	[1] = 0, -- 第一技能上次使用时间
	[2] = 0, -- 第二技能上次使用时间
	[3] = 0  -- 第三技能上次使用时间
}

-- 添加远程武器相关变量
WeaponClient.IsShooting = false
WeaponClient.LastShootTime = 0
WeaponClient.CurrentAmmo = 0
WeaponClient.IsReloading = false
WeaponClient.ShootingLoop = nil -- 自动射击循环

-- 添加十字准星UI变量
WeaponClient.Crosshair = nil
WeaponClient.DefaultMouseIcon = nil  -- 存储默认鼠标图标

-- 添加弹药UI变量
WeaponClient.AmmoUI = nil

-- 创建弹药UI
function WeaponClient:CreateAmmoUI()
	local player = Players.LocalPlayer
	if not player then return end

	-- 检查是否已存在弹药UI
	if self.AmmoUI and self.AmmoUI.Parent then
		return self.AmmoUI
	end

	-- 获取或创建PlayerGui
	local playerGui = player:FindFirstChild("PlayerGui")
	if not playerGui then
		playerGui = Instance.new("ScreenGui")
		playerGui.Name = "PlayerGui"
		playerGui.Parent = player
	end

	-- 创建弹药UI的ScreenGui
	local ammoScreenGui = Instance.new("ScreenGui")
	ammoScreenGui.Name = "AmmoUI"
	ammoScreenGui.ResetOnSpawn = false
	ammoScreenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
	ammoScreenGui.Parent = playerGui

	-- 创建弹药显示框架
	local ammoFrame = Instance.new("Frame")
	ammoFrame.Name = "AmmoFrame"
	ammoFrame.Size = UDim2.new(0, 200, 0, 80)
	ammoFrame.Position = UDim2.new(1, -220, 1, -100) -- 右下角位置
	ammoFrame.BackgroundTransparency = 1
	ammoFrame.Parent = ammoScreenGui

	-- 创建当前弹药文本
	local currentAmmoText = Instance.new("TextLabel")
	currentAmmoText.Name = "CurrentAmmo"
	currentAmmoText.Size = UDim2.new(0, 100, 0, 50)
	currentAmmoText.Position = UDim2.new(0, 0, 0, 0)
	currentAmmoText.BackgroundTransparency = 1
	currentAmmoText.Font = Enum.Font.SourceSansBold
	currentAmmoText.TextSize = 36
	currentAmmoText.TextColor3 = Color3.new(1, 1, 1)
	currentAmmoText.Text = "0"
	currentAmmoText.TextXAlignment = Enum.TextXAlignment.Right
	currentAmmoText.Parent = ammoFrame

	-- 创建分隔符
	local separatorText = Instance.new("TextLabel")
	separatorText.Name = "Separator"
	separatorText.Size = UDim2.new(0, 20, 0, 50)
	separatorText.Position = UDim2.new(0, 100, 0, 0)
	separatorText.BackgroundTransparency = 1
	separatorText.Font = Enum.Font.SourceSansBold
	separatorText.TextSize = 36
	separatorText.TextColor3 = Color3.new(1, 1, 1)
	separatorText.Text = "/"
	separatorText.Parent = ammoFrame

	-- 创建弹夹容量文本
	local maxAmmoText = Instance.new("TextLabel")
	maxAmmoText.Name = "MaxAmmo"
	maxAmmoText.Size = UDim2.new(0, 80, 0, 50)
	maxAmmoText.Position = UDim2.new(0, 120, 0, 0)
	maxAmmoText.BackgroundTransparency = 1
	maxAmmoText.Font = Enum.Font.SourceSansBold
	maxAmmoText.TextSize = 36
	maxAmmoText.TextColor3 = Color3.new(1, 1, 1)
	maxAmmoText.Text = "0"
	maxAmmoText.TextXAlignment = Enum.TextXAlignment.Left
	maxAmmoText.Parent = ammoFrame

	self.AmmoUI = ammoScreenGui
	return ammoScreenGui
end

-- 更新弹药UI显示
function WeaponClient:UpdateAmmoUI()
	if not self.AmmoUI then
		self:CreateAmmoUI()
	end

	local ammoFrame = self.AmmoUI:FindFirstChild("AmmoFrame")
	if not ammoFrame then return end

	local currentAmmoText = ammoFrame:FindFirstChild("CurrentAmmo")
	local maxAmmoText = ammoFrame:FindFirstChild("MaxAmmo")

	if currentAmmoText and maxAmmoText then
		local maxAmmo = self.RemoteData and self.RemoteData.AmmoCapacity or 0
		currentAmmoText.Text = tostring(self.CurrentAmmo)
		maxAmmoText.Text = tostring(maxAmmo)
	end
end

-- 显示或隐藏弹药UI
function WeaponClient:SetAmmoUIVisibility(visible)
	if not self.AmmoUI then
		if visible then
			self:CreateAmmoUI()
		else
			return
		end
	end

	self.AmmoUI.Enabled = visible

	if visible then
		self:UpdateAmmoUI()
	end
end

-- 设置鼠标为小型十字准星
function WeaponClient:SetDotCursor()
	local player = Players.LocalPlayer
	if not player then return end

	local mouse = player:GetMouse()
	if mouse then
		-- 使用十字准星但缩小成点状
		-- 创建一个小型十字准星图标
		local crosshairId = "rbxassetid://137228710467615" -- 这是一个小型十字准星的资源ID
		mouse.Icon = crosshairId
		print("已设置鼠标为小型十字准星: " .. crosshairId)

		-- 确保鼠标图标设置成功的备用方法
		spawn(function()
			wait(0.1)  -- 短暂等待确保设置生效
			if mouse.Icon ~= crosshairId then
				mouse.Icon = crosshairId
				print("重新尝试设置鼠标图标")
			end
		end)
	end
end

-- 初始化（角色重生时调用）
function WeaponClient:Initialize()
	local player = Players.LocalPlayer

	-- 立即设置鼠标为白色小点
	self:SetDotCursor()

	-- 创建弹药UI
	self:CreateAmmoUI()
	self:SetAmmoUIVisibility(false) -- 默认隐藏

	-- 注册服务端通知事件
	NotifyService.RegisterClientEvent("WeaponSkillCooldown", function(data)
		-- 处理技能冷却通知
		if data.skillIndex and data.remainingTime then
			self.SkillCooldowns[data.skillIndex] = tick() - data.remainingTime + data.cooldownTime
		end
	end)

	-- 注册换弹取消事件
	NotifyService.RegisterClientEvent("ReloadCancelled", function()
		-- 取消换弹状态
		if self.IsReloading then
			print("换弹被取消")
			self.IsReloading = false

			-- 停止所有换弹音效
			local player = Players.LocalPlayer
			if player and player.Character then
				for _, child in pairs(player.Character:GetDescendants()) do
					if self.RemoteData and self.RemoteData.SwapSoundEffect and child:IsA("Sound") and child.SoundId == self.RemoteData.SwapSoundEffect then
						child:Stop()
						child:Destroy()
					end
				end
			end
		end
	end)

	NotifyService.RegisterClientEvent("WeaponEquipped", function(data)
		-- 处理武器装备通知
		if data.weaponId then
			print("收到武器装备通知:", data.weaponName)

			-- 更新武器图标
			if data.weaponIcon and data.weaponIcon ~= "" then
				-- 查找玩家背包中的对应武器
				local player = Players.LocalPlayer
				local tool = nil

				-- 先检查角色中是否有装备的武器
				if player.Character then
					for _, child in pairs(player.Character:GetChildren()) do
						if child:IsA("Tool") and child.Name == data.weaponName then
							tool = child
							break
						end
					end
				end

				-- 如果角色中没找到，检查背包
				if not tool and player:FindFirstChild("Backpack") then
					for _, child in pairs(player.Backpack:GetChildren()) do
						if child:IsA("Tool") and child.Name == data.weaponName then
							tool = child
							break
						end
					end
				end

				-- 如果找到了武器，更新其图标
				if tool then
					print("从WeaponEquipped事件更新武器图标: " .. data.weaponIcon)
					self:UpdateWeaponTexture(tool, data.weaponIcon)
				end
			end

			-- 这里可以添加更新UI的代码
			-- 查找并更新热键栏UI
			local playerGui = Players.LocalPlayer:FindFirstChild("PlayerGui")
			if playerGui then
				local hotbarUI = playerGui:FindFirstChild("HotbarUI") or playerGui:FindFirstChild("InventoryUI")
				if hotbarUI then
					-- 尝试通知UI系统刷新
					local refreshEvent = hotbarUI:FindFirstChild("RefreshEvent")
					if refreshEvent and refreshEvent:IsA("BindableEvent") then
						refreshEvent:Fire()
					end
				end
			end
		end
	end)

	-- 添加濒死状态监听，确保在进入濒死状态时停止所有武器操作
	NotifyService.RegisterClientEvent("PlayerDowned", function(data)
		-- 玩家进入濒死状态，停止所有武器操作
		if self.IsShooting then
			self:StopShooting()
		end

		if self.IsReloading then
			-- 通知服务端取消换弹
			ProtocolManager.SendMessage("ReloadCancelledEvent", {
				weaponName = self.WeaponData and self.WeaponData.Name or ""
			})
			self.IsReloading = false
		end

		-- 如果有武器装备中，卸下武器
		if self.IsWeaponEquipped and player.Character then
			local tool = player.Character:FindFirstChildOfClass("Tool")
			if tool then
				tool.Parent = player.Backpack
			end
		end

		-- 隐藏弹药UI
		self:SetAmmoUIVisibility(false)
	end)

	-- 添加恢复状态监听
	NotifyService.RegisterClientEvent("PlayerRevived", function(data)
		-- 玩家恢复正常，不需要特别处理，让用户重新装备武器即可
	end)

	NotifyService.RegisterClientEvent("PlayAttackEffect", function(data)
		-- 确保不是自己的攻击特效
		if data.playerName ~= Players.LocalPlayer.Name then
			print("收到其他玩家的攻击特效:", data.effectName, "玩家:", data.playerName)
			self:PlayAttackEffect(data.effectName, data.position, data.direction)
		end
	end)

	NotifyService.RegisterClientEvent("BulletCreated", function(data)
		-- 确保不是自己发射的子弹
		if data.playerName ~= Players.LocalPlayer.Name then
			print("收到其他玩家的子弹:", data.bulletModel, "玩家:", data.playerName)

			-- 改进的子弹创建
			local bulletTemplate = nil
			local bulletFolder = ReplicatedStorage:FindFirstChild("Model")
			if bulletFolder then
				bulletFolder = bulletFolder:FindFirstChild("Equip")
				if bulletFolder then
					bulletFolder = bulletFolder:FindFirstChild("Bullet")
					if bulletFolder then
						bulletTemplate = bulletFolder:FindFirstChild(data.bulletModel)
					end
				end
			end

			if bulletTemplate then
				local bullet = bulletTemplate:Clone()
				bullet.Parent = workspace
				bullet.Name = "Bullet_" .. data.playerName .. "_" .. tostring(tick())

				-- 根据子弹类型进行不同的处理
				if bullet:IsA("BasePart") then
					-- BasePart类型子弹处理
					self:SetupNetworkBasePartBullet(bullet, data)
				elseif bullet:IsA("Model") then
					-- Model类型子弹处理
					self:SetupNetworkModelBullet(bullet, data)
				else
					warn("不支持的网络子弹类型: " .. bullet.ClassName)
					bullet:Destroy()
				end
			end
		end
	end)

	NotifyService.RegisterClientEvent("AmmoUpdated", function(data)
		if data.currentAmmo ~= nil then
			self.CurrentAmmo = data.currentAmmo
			print("弹药更新:", self.CurrentAmmo)
			-- 更新UI弹药显示
			self:UpdateAmmoUI()
		end
	end)

	NotifyService.RegisterClientEvent("ReloadStarted", function(data)
		-- 只有在当前装备的武器匹配时才设置换弹状态
		if self.IsWeaponEquipped and self.WeaponData and self.RemoteData then
			self.IsReloading = true
			print("开始换弹: " .. self.WeaponData.Name)

			-- 播放换弹动画和音效
			if self.RemoteData.SwapSoundEffect and self.RemoteData.SwapSoundEffect ~= "" then
				local sound = Instance.new("Sound")
				sound.SoundId = self.RemoteData.SwapSoundEffect
				sound.Volume = 1
				local player = Players.LocalPlayer
				if player and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
					sound.Parent = player.Character.HumanoidRootPart
				else
					sound:Destroy()
					return
				end
				sound:Play()
				game:GetService("Debris"):AddItem(sound, 3)
			end
		else
			print("收到换弹开始事件，但当前没有装备匹配的武器")
		end
	end)

	NotifyService.RegisterClientEvent("ReloadFinished", function(data)
		-- 只有在当前处于换弹状态时才处理换弹完成
		if self.IsReloading and self.IsWeaponEquipped and self.WeaponData then
			self.IsReloading = false
			self.CurrentAmmo = data.newAmmo
			print("换弹完成: " .. self.WeaponData.Name .. ", 当前弹药:", self.CurrentAmmo)
			-- 更新UI弹药显示
			self:UpdateAmmoUI()
		else
			print("收到换弹完成事件，但当前状态不匹配")
		end
	end)

	-- 每次角色重生时都重置状态
	local function setupCharacter(character)
		if not character then return end

		-- 重置所有状态
		self.CurrentWeapon = nil
		self.IsWeaponEquipped = false
		self.ComboIndex = 1
		self.LastAttackTime = 0
		self.WeaponData = nil
		self.MeleeData = nil
		self.RemoteData = nil
		self.Skills = {}
		self.SkillCooldowns = {[1] = 0, [2] = 0, [3] = 0}
		self.IsShooting = false
		self.LastShootTime = 0
		self.CurrentAmmo = 0
		self.IsReloading = false

		-- 停止射击循环
		if self.ShootingLoop then
			self.ShootingLoop:Disconnect()
			self.ShootingLoop = nil
		end

		-- 确保鼠标设置为白色小点
		self:SetDotCursor()

		if self.CurrentAnimTrack then
			self.CurrentAnimTrack:Stop()
			self.CurrentAnimTrack = nil
		end

		-- 等待人形出现并获取动画器
		local humanoid = character:WaitForChild("Humanoid")
		self.animator = humanoid:FindFirstChildOfClass("Animator")
		if not self.animator then
			self.animator = Instance.new("Animator")
			self.animator.Parent = humanoid
		end

		-- 监听工具装备
		character.ChildAdded:Connect(function(child)
			if child:IsA("Tool") then
				self:OnToolEquipped(child)
			end
		end)

		character.ChildRemoved:Connect(function(child)
			if child:IsA("Tool") then
				self:OnToolUnequipped(child)
			end
		end)

		-- 检查是否已经装备了工具
		for _, child in pairs(character:GetChildren()) do
			if child:IsA("Tool") then
				self:OnToolEquipped(child)
			end
		end

		-- 隐藏弹药UI
		self:SetAmmoUIVisibility(false)
	end

	-- 初始角色设置
	if player.Character then
		setupCharacter(player.Character)
	end

	-- 监听玩家角色重生
	player.CharacterAdded:Connect(setupCharacter)
end

-- 根据模型名称查找武器配置
function WeaponClient:FindWeaponDataByName(modelName)
	for _, weaponData in ipairs(WeaponConfig) do
		if weaponData.WeaponModeling == modelName then
			return weaponData
		end
	end
	return nil
end

-- 根据武器ID查找近战武器配置
function WeaponClient:FindMeleeDataById(weaponId)
	for _, meleeData in ipairs(MeleeConfig) do
		if meleeData.Id == weaponId then
			return meleeData
		end
	end
	return nil
end

-- 根据武器ID查找远程武器配置
function WeaponClient:FindRemoteDataById(weaponId)
	for _, remoteData in ipairs(RemoteWeaponConfig) do
		if remoteData.Id == weaponId then
			return remoteData
		end
	end
	return nil
end

-- 根据ID查找技能配置
function WeaponClient:FindSkillDataById(skillId)
	for _, skillData in ipairs(SkillConfig) do
		if skillData.Id == skillId then
			return skillData
		end
	end
	return nil
end

-- 生成武器实例唯一标识符
function WeaponClient:GenerateWeaponInstanceId(weaponName)
	return weaponName .. "_" .. tostring(tick()) .. "_" .. tostring(math.random(1000, 9999))
end

-- 从工具获取或创建实例ID
function WeaponClient:GetOrCreateWeaponInstanceId(tool)
	if not tool then return nil end

	-- 检查工具是否已有实例ID
	local instanceIdValue = tool:FindFirstChild("WeaponInstanceId")
	if instanceIdValue and instanceIdValue:IsA("StringValue") then
		return instanceIdValue.Value
	end

	-- 创建新的实例ID
	local instanceId = self:GenerateWeaponInstanceId(tool.Name)
	local idValue = Instance.new("StringValue")
	idValue.Name = "WeaponInstanceId"
	idValue.Value = instanceId
	idValue.Parent = tool

	print("为武器创建实例ID: " .. tool.Name .. " -> " .. instanceId)
	return instanceId
end

function WeaponClient:UpdateWeaponTexture(tool, iconId)
	-- 设置工具的TextureId属性
	if tool and iconId and iconId ~= "" then
		-- 设置工具栏图标
		tool.TextureId = iconId
		print("已更新工具TextureId: " .. iconId)

		-- 确保工具有一个图标值用于工具栏显示
		local iconValue = tool:FindFirstChild("ToolbarIcon")
		if not iconValue then
			iconValue = Instance.new("StringValue")
			iconValue.Name = "ToolbarIcon"
			iconValue.Parent = tool
		end
		iconValue.Value = iconId
	end
end

-- 刷新武器图标
function WeaponClient:RefreshWeaponIcon(tool)
	if not tool or not self.WeaponData then return end

	-- 确保工具有正确的图标
	if self.WeaponData.Icon and self.WeaponData.Icon ~= "" then
		self:UpdateWeaponTexture(tool, self.WeaponData.Icon)

		-- 通知UI系统刷新
		local playerGui = Players.LocalPlayer:FindFirstChild("PlayerGui")
		if playerGui then
			local hotbarUI = playerGui:FindFirstChild("HotbarUI") or playerGui:FindFirstChild("InventoryUI")
			if hotbarUI then
				-- 尝试通知UI系统刷新
				local refreshEvent = hotbarUI:FindFirstChild("RefreshEvent")
				if refreshEvent and refreshEvent:IsA("BindableEvent") then
					refreshEvent:Fire()
				end
			end
		end
	end
end

-- 检查玩家是否处于濒死状态的辅助函数
function WeaponClient:IsPlayerDowned()
	local player = Players.LocalPlayer
	if not player then return false end

	-- 检查玩家的IsDowned属性
	if player:GetAttribute("IsDowned") then
		return true
	end

	-- 检查角色的IsDowned属性
	local character = player.Character
	if character and character:GetAttribute("IsDowned") then
		return true
	end

	-- 检查DisablePickup属性
	if player:GetAttribute("DisablePickup") then
		return true
	end

	return false
end

function WeaponClient:OnToolEquipped(tool)
	-- 检查玩家是否处于濒死状态，如果是则立即卸载武器
	if self:IsPlayerDowned() then
		print("玩家处于濒死状态，无法装备武器，自动卸载")
		local player = Players.LocalPlayer
		if player and player.Backpack then
			tool.Parent = player.Backpack
		end
		return
	end

	-- 查找武器配置
	local weaponData = self:FindWeaponDataByName(tool.Name)
	if weaponData then
		-- 关联武器配置
		self.CurrentWeapon = tool
		self.IsWeaponEquipped = true
		self.WeaponData = weaponData
		self.ComboIndex = 1

		-- 根据武器类型加载对应配置
		if weaponData.WeaponType == 1 then -- 近战武器
			self.MeleeData = self:FindMeleeDataById(weaponData.Id)
			self.RemoteData = nil
			print("装备近战武器: " .. weaponData.Name)

			-- 关联技能配置
			self.Skills = {}
			if self.MeleeData then
				if self.MeleeData.FirstAttackSkill then
					local skillData = self:FindSkillDataById(self.MeleeData.FirstAttackSkill)
					if skillData then
						self.Skills[1] = skillData
					end
				end

				if self.MeleeData.SecondAttackSkill then
					local skillData = self:FindSkillDataById(self.MeleeData.SecondAttackSkill)
					if skillData then
						self.Skills[2] = skillData
					end
				end

				if self.MeleeData.ThirdAttackSkill then
					local skillData = self:FindSkillDataById(self.MeleeData.ThirdAttackSkill)
					if skillData then
						self.Skills[3] = skillData
					end
				end
			end

			-- 隐藏弹药UI
			self:SetAmmoUIVisibility(false)
		elseif weaponData.WeaponType == 2 then -- 远程武器
			self.MeleeData = nil
			self.RemoteData = self:FindRemoteDataById(weaponData.Id)
			print("装备远程武器: " .. weaponData.Name)

			-- 重置换弹状态（重要：确保装备武器时状态正确）
			self.IsReloading = false

			-- 显示弹药UI
			self:SetAmmoUIVisibility(true)

			-- 获取或创建武器实例ID
			local weaponInstanceId = self:GetOrCreateWeaponInstanceId(tool)

			-- 初始化弹药 - 先设置为0，等待服务端同步
			if self.RemoteData then
				self.CurrentAmmo = 0  -- 不再立即设置为满弹夹
				self:UpdateAmmoUI()

				-- 通知服务端装备事件，包含实例ID
				ProtocolManager.SendMessage("WeaponEquipEvent", {
					weaponId = weaponData.Id,
					remoteWeaponId = self.RemoteData.Id,
					weaponInstanceId = weaponInstanceId,
					ammo = 0  -- 让服务端决定弹药数量
				})
			end

			-- 启动远程武器跟随功能（延迟启动确保武器完全装备）
			local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)
			if CameraControlService:IsFirstPerson() then
				-- 延迟启动，确保武器Handle完全初始化
				spawn(function()
					wait(0.1)  -- 短暂延迟

					-- 安全获取player对象
					local player = Players.LocalPlayer
					if not player then
						print("⚠️ 无法获取LocalPlayer，跳过武器跟随启动")
						return
					end

					-- 检查player是否有Character
					if not player.Character then
						print("⚠️ 玩家角色不存在，跳过武器跟随启动")
						return
					end

					-- 确保武器仍然装备着
					if tool.Parent == player.Character then
						local followSuccess = CameraControlService:StartWeaponCameraFollow(tool)
						if followSuccess then
							print("✅ 远程武器镜头跟随已启动: " .. weaponData.Name)
						else
							print("⚠️ 远程武器镜头跟随启动失败: " .. weaponData.Name)
							-- 尝试诊断失败原因
							local handle = tool:FindFirstChild("Handle")
							if not handle then
								print("  原因: 武器缺少Handle")
							else
								print("  Handle存在，其他原因导致失败")
							end
						end
					else
						print("⚠️ 武器在延迟期间被卸载，跳过跟随启动")
					end
				end)
			else
				print("⚠️ 非第一人称视角，跳过武器跟随启动")
			end

			-- 远程武器不加载近战技能
			self.Skills = {}
		else
			-- 无效武器类型
			self.MeleeData = nil
			self.RemoteData = nil
			self.Skills = {}

			-- 隐藏弹药UI
			self:SetAmmoUIVisibility(false)

			print("无效的武器类型: " .. (weaponData.WeaponType or "未知"))
		end

		-- 重置技能冷却
		self.SkillCooldowns = {[1] = 0, [2] = 0, [3] = 0}

		-- 更新武器纹理 - 确保这段代码执行
		if weaponData.Icon and weaponData.Icon ~= "" then
			print("武器图标ID: " .. weaponData.Icon)
			self:UpdateWeaponTexture(tool, weaponData.Icon)

			-- 强制刷新UI
			self:RefreshWeaponIcon(tool)
		else
			print("武器没有设置Icon")
		end

		-- 检查工具是否缺少必要的部件，如果是，修复它
		self:EnsureToolHasRequiredParts(tool, weaponData)

		print("已装备武器: " .. weaponData.Name)
	end
end

-- 确保工具有所需的部件（从死亡盒子恢复后可能缺少模型部件）
function WeaponClient:EnsureToolHasRequiredParts(tool, weaponData)
	-- 检查工具是否有Handle部件
	if not tool:FindFirstChild("Handle") then
		print("工具缺少Handle部件，尝试修复: " .. tool.Name)

		-- 尝试从ReplicatedStorage获取预制模型
		local modelFound = false
		local equipFolder = ReplicatedStorage:FindFirstChild("Model")
		if equipFolder then
			equipFolder = equipFolder:FindFirstChild("Equip")
			if equipFolder then
				local template = nil

				-- 使用统一的Weapon文件夹
				local weaponFolder = equipFolder:FindFirstChild("Weapon")
				if weaponFolder then
					template = weaponFolder:FindFirstChild(tool.Name)
				end

				-- 如果在Weapon文件夹中没找到，尝试在Equip根目录查找
				if not template then
					template = equipFolder:FindFirstChild(tool.Name)
				end

				if template and template:IsA("Tool") then
					-- 找到了预制模型，复制其子部件
					for _, child in pairs(template:GetChildren()) do
						if not tool:FindFirstChild(child.Name) then
							local clone = child:Clone()
							clone.Parent = tool
						end
					end
					modelFound = true
					print("已从模板修复工具模型: " .. tool.Name)
				end
			end
		end

		-- 如果找不到预制模型，创建一个基本的Handle
		if not modelFound then
			print("找不到模板，创建基本Handle: " .. tool.Name)
			local handle = Instance.new("Part")
			handle.Name = "Handle"
			handle.Size = Vector3.new(1, 4, 1)
			handle.BrickColor = BrickColor.new("Really black")
			handle.Material = Enum.Material.Metal
			handle.Parent = tool

			-- 如果有图标，可以添加一个纹理
			if weaponData.Icon and weaponData.Icon ~= "" then
				local texture = Instance.new("Texture")
				texture.Texture = weaponData.Icon
				texture.Face = Enum.NormalId.Right
				texture.Parent = handle
			end
		end
	end
end

function WeaponClient:OnToolUnequipped(tool)
	local weaponData = self:FindWeaponDataByName(tool.Name)
	if weaponData then
		-- 如果是远程武器，停止射击状态和武器跟随
		if self.RemoteData then
			self:StopShooting()

			-- 停止远程武器跟随功能
			local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)
			CameraControlService:StopWeaponCameraFollow()
			print("✅ 远程武器卸载：已停止镜头跟随")

			-- 确保取消换弹状态
			if self.IsReloading then
				print("工具卸下，取消换弹状态")
				self.IsReloading = false

				-- 停止所有换弹音效
				if Players.LocalPlayer.Character then
					for _, child in pairs(Players.LocalPlayer.Character:GetDescendants()) do
						if child:IsA("Sound") and child.SoundId == self.RemoteData.SwapSoundEffect then
							child:Stop()
							child:Destroy()
						end
					end
				end

				-- 通知服务器取消换弹状态
				ProtocolManager.SendMessage("ReloadCancelledEvent", {
					weaponName = tool.Name
				})
			end
		end

		self.CurrentWeapon = nil
		self.IsWeaponEquipped = false
		self.WeaponData = nil
		self.MeleeData = nil
		self.RemoteData = nil
		self.Skills = {}
		self.ComboIndex = 1
		-- 重要：保留当前弹药数量，以便后续恢复（包括0弹药）
		local savedAmmo = self.CurrentAmmo
		print("武器卸下，保存弹药状态: " .. tool.Name .. ", 弹药: " .. savedAmmo)

		self.CurrentAmmo = 0
		self.IsReloading = false  -- 确保重置换弹状态

		-- 停止当前播放的动画
		if self.CurrentAnimTrack then
			self.CurrentAnimTrack:Stop()
			self.CurrentAnimTrack = nil
		end

		-- 隐藏弹药UI
		self:SetAmmoUIVisibility(false)

		-- 获取武器实例ID
		local weaponInstanceId = self:GetOrCreateWeaponInstanceId(tool)

		-- 通知服务器武器已卸下，需要保存弹药状态（即使为0）
		ProtocolManager.SendMessage("WeaponUnequippedEvent", {
			weaponName = tool.Name,
			weaponInstanceId = weaponInstanceId,
			savedAmmo = savedAmmo -- 确保传递真实的弹药数量，包括0
		})

		print("已发送武器卸下事件: " .. tool.Name .. ", 实例ID: " .. (weaponInstanceId or "无") .. ", 保存弹药: " .. savedAmmo)
	end
end

-- 检查技能是否在冷却中
function WeaponClient:IsSkillOnCooldown(skillIndex)
	local now = tick()
	local lastUseTime = self.SkillCooldowns[skillIndex] or 0
	local skillData = self.Skills[skillIndex]

	if not skillData then
		return true -- 如果没有该技能，视为冷却中
	end

	local cooldownTime = skillData.Cooldown or 0.5
	return (now - lastUseTime) < cooldownTime
end

-- 添加在WeaponClient里的新函数，用于播放攻击特效
function WeaponClient:PlayAttackEffect(effectName, position, direction)
	if not effectName or effectName == "" then return end

	-- 查找特效模板
	local effectsFolder = ReplicatedStorage:FindFirstChild("Effect")
	if not effectsFolder then
		print("未找到特效文件夹")
		return
	end

	-- 查找特定特效
	local effectTemplate

	-- 先查找SwordSlashthing文件夹
	local swordEffects = effectsFolder:FindFirstChild("SwordSlashthing")
	if swordEffects then
		effectTemplate = swordEffects:FindFirstChild(effectName)
	end

	-- 如果在SwordSlashthing中没找到，则在Effect根目录查找
	if not effectTemplate then
		effectTemplate = effectsFolder:FindFirstChild(effectName)
	end

	if not effectTemplate then
		print("未找到特效: " .. effectName)
		return
	end

	-- 克隆特效并设置位置
	local effect = effectTemplate:Clone()
	effect.Parent = workspace

	-- 设置特效位置和方向
	if direction then
		local lookAt = position + direction * 5
		effect.CFrame = CFrame.lookAt(position, lookAt)
	else
		effect.Position = position
	end

	-- 播放特效（如果有ParticleEmitter或其他需要启动的组件）
	for _, child in pairs(effect:GetDescendants()) do
		if child:IsA("ParticleEmitter") then
			child.Enabled = true
		elseif child:IsA("Sound") then
			child:Play()
		elseif child:IsA("Trail") then
			child.Enabled = true
		end
	end

	-- 如果特效有Animation，播放它
	local animationController = effect:FindFirstChildOfClass("AnimationController")
	if animationController then
		local animator = animationController:FindFirstChildOfClass("Animator")
		if animator then
			for _, anim in pairs(effect:GetDescendants()) do
				if anim:IsA("Animation") then
					local track = animator:LoadAnimation(anim)
					track:Play()
				end
			end
		end
	end

	-- 处理特效自动删除
	-- 方法1：使用Debris服务
	game:GetService("Debris"):AddItem(effect, 5)

	-- 方法2：使用自定义计时器（如果特效需要更精确的控制）
	spawn(function()
		local duration = 3  -- 默认持续3秒
		wait(duration)
		-- 淡出特效
		for i = 1, 10 do
			if effect and effect.Parent then
				for _, child in pairs(effect:GetDescendants()) do
					if child:IsA("ParticleEmitter") then
						child.Enabled = false
					end
				end
				effect.Transparency = i/10
			end
			wait(0.1)
		end
		-- 删除特效
		if effect and effect.Parent then
			effect:Destroy()
		end
	end)

	return effect
end

-- 修改UseSkill函数，添加特效播放
function WeaponClient:UseSkill(skillIndex)
	if not self.IsWeaponEquipped or not self.animator or not self.WeaponData then 
		return false
	end

	-- 检查技能是否在冷却中
	if self:IsSkillOnCooldown(skillIndex) then
		print("技能" .. skillIndex .. "冷却中")
		return false
	end

	-- 获取技能数据
	local skillData = self.Skills[skillIndex]
	if not skillData then
		print("没有找到技能" .. skillIndex)
		return false
	end

	-- 更新冷却时间
	self.SkillCooldowns[skillIndex] = tick()

	-- 停止当前播放的动画
	if self.CurrentAnimTrack then
		self.CurrentAnimTrack:Stop()
	end

	-- 获取角色位置和朝向信息，用于特效
	local character = Players.LocalPlayer.Character
	local rootPart = character and character:FindFirstChild("HumanoidRootPart")
	if rootPart then
		local position = rootPart.Position
		local direction = rootPart.CFrame.LookVector

		-- 播放攻击特效
		if skillData.AttackSpecialEffects and skillData.AttackSpecialEffects ~= "" then
			print("播放攻击特效: " .. skillData.AttackSpecialEffects)

			-- 计算特效位置（在玩家前方）
			local effectPosition = position + direction * 5

			-- 播放特效
			self:PlayAttackEffect(skillData.AttackSpecialEffects, effectPosition, direction)
		end
	end

	-- 播放技能动画
	if skillData.AttackAction and self.animator then
		local animationId = skillData.AttackAction
		local animation = Instance.new("Animation")
		animation.AnimationId = animationId

		self.CurrentAnimTrack = self.animator:LoadAnimation(animation)
		self.CurrentAnimTrack:Play()

		print("释放技能: " .. skillData.Chinese .. " - " .. skillData.Name)
	end

	-- 播放攻击音效
	if skillData.AttackSoundEffects and skillData.AttackSoundEffects ~= "" then
		local sound = Instance.new("Sound")
		sound.SoundId = skillData.AttackSoundEffects
		sound.Volume = 1
		sound.Parent = Players.LocalPlayer.Character.HumanoidRootPart
		sound:Play()
		game:GetService("Debris"):AddItem(sound, 3)
	end

	-- 通知服务端攻击和当前技能索引，同时传递特效信息
	local position = Players.LocalPlayer.Character.HumanoidRootPart.Position
	local direction = Players.LocalPlayer.Character.HumanoidRootPart.CFrame.LookVector

	ProtocolManager.SendMessage("PlayerAttackEvent", {
		skillIndex = skillIndex,
		position = position,
		direction = direction,
		effectName = skillData.AttackSpecialEffects or "",
		playerName = Players.LocalPlayer.Name
	})

	return true
end

-- 普通攻击(第一技能)
function WeaponClient:Attack()
	if self:UseSkill(1) then
		-- 普通攻击成功
		self.LastAttackTime = tick()

		-- 更新连招计数（仅对普通攻击）
		self.ComboIndex = self.ComboIndex + 1
		if self.ComboIndex > 1 then -- 普通攻击不再有连招
			self.ComboIndex = 1
		end
	end
end

-- 释放第二技能
function WeaponClient:UseSecondSkill()
	return self:UseSkill(2)
end

-- 释放第三技能
function WeaponClient:UseThirdSkill()
	return self:UseSkill(3)
end

-- 创建子弹函数
function WeaponClient:CreateBullet(bulletModel, startPosition, direction, speed)
	-- 从正确路径获取子弹模型
	local bulletTemplate = nil
	local bulletFolder = ReplicatedStorage:FindFirstChild("Model")
	if bulletFolder then
		bulletFolder = bulletFolder:FindFirstChild("Equip")
		if bulletFolder then
			bulletFolder = bulletFolder:FindFirstChild("Bullet")
			if bulletFolder then
				bulletTemplate = bulletFolder:FindFirstChild(bulletModel)
			end
		end
	end

	if not bulletTemplate then
		print("未找到子弹模型:", bulletModel)
		return
	end

	-- 克隆子弹模型
	local bullet = bulletTemplate:Clone()
	bullet.Parent = workspace

	-- 设置子弹初始位置和方向
	if bullet:IsA("BasePart") then
		-- BasePart类型子弹处理
		self:SetupBasePartBullet(bullet, startPosition, direction, speed)
	elseif bullet:IsA("Model") then
		-- Model类型子弹处理（新增支持）
		self:SetupModelBullet(bullet, startPosition, direction, speed)
	else
		warn("不支持的子弹类型: " .. bullet.ClassName)
		bullet:Destroy()
	end
end

-- 设置BasePart类型子弹（保持原有逻辑）
function WeaponClient:SetupBasePartBullet(bullet, startPosition, direction, speed)
	bullet.CFrame = CFrame.new(startPosition, startPosition + direction)
	bullet.CanCollide = false

	-- 简单设置子弹速度
	local velocity = Instance.new("LinearVelocity")
	velocity.MaxForce = 1000000
	velocity.VectorVelocity = direction.Unit * speed
	velocity.Parent = bullet

	-- 简单的子弹碰撞检测
	local function handleBulletCollision(hit)
		if hit and hit.Parent then
			-- 直接销毁子弹
			bullet:Destroy()
		end
	end

	bullet.Touched:Connect(handleBulletCollision)

	-- 设置子弹自动销毁（超出最大射程）
	local maxDistance = self.RemoteData and self.RemoteData.Range or 100
	local maxLifetime = maxDistance / speed

	spawn(function()
		local startTime = tick()
		local initialPosition = startPosition

		while bullet and bullet.Parent do
			if (bullet.Position - initialPosition).Magnitude > maxDistance or (tick() - startTime) > maxLifetime then
				bullet:Destroy()
				break
			end
			wait(0.1)
		end
	end)
end

-- 设置Model类型子弹（新增方法）
function WeaponClient:SetupModelBullet(bullet, startPosition, direction, speed)
	-- 确保Model有PrimaryPart
	if not bullet.PrimaryPart then
		local mainPart = bullet:FindFirstChild("Core") or
			bullet:FindFirstChild("HumanoidRootPart") or
			bullet:FindFirstChild("Torso") or
			bullet:FindFirstChildOfClass("BasePart")

		if mainPart then
			bullet.PrimaryPart = mainPart
			print("为Model子弹设置PrimaryPart: " .. mainPart.Name)
		else
			warn("Model类型子弹缺少PrimaryPart: " .. bullet.Name)
			bullet:Destroy()
			return
		end
	end

	local primaryPart = bullet.PrimaryPart

	-- 设置初始位置和方向
	bullet:SetPrimaryPartCFrame(CFrame.new(startPosition, startPosition + direction))

	-- 设置物理属性
	primaryPart.CanCollide = false

	-- 为Model中的所有Part设置物理属性并创建连接
	for _, part in ipairs(bullet:GetDescendants()) do
		if part:IsA("BasePart") and part ~= primaryPart then
			part.CanCollide = false
			part.Anchored = false  -- 确保子部件不被锚定

			-- 创建WeldConstraint将子部件连接到PrimaryPart
			local weld = Instance.new("WeldConstraint")
			weld.Part0 = primaryPart
			weld.Part1 = part
			weld.Parent = primaryPart

			print("简单版：已将 " .. part.Name .. " 连接到 " .. primaryPart.Name)
		end
	end

	-- 使用LinearVelocity控制Model子弹运动
	local velocity = Instance.new("LinearVelocity")
	velocity.MaxForce = 1000000
	velocity.VectorVelocity = direction.Unit * speed
	velocity.Parent = primaryPart

	-- Model子弹碰撞检测
	local function handleModelBulletCollision(hit)
		if hit and hit.Parent and not hit:IsDescendantOf(bullet) then
			-- 排除子弹自身的部件
			bullet:Destroy()
		end
	end

	-- 为Model中的所有Part设置碰撞检测
	for _, part in ipairs(bullet:GetDescendants()) do
		if part:IsA("BasePart") then
			part.Touched:Connect(handleModelBulletCollision)
		end
	end

	-- 设置子弹自动销毁（超出最大射程）
	local maxDistance = self.RemoteData and self.RemoteData.Range or 100
	local maxLifetime = maxDistance / speed

	spawn(function()
		local startTime = tick()
		local initialPosition = startPosition

		while bullet and bullet.Parent and bullet.PrimaryPart do
			local currentPosition = bullet.PrimaryPart.Position
			if (currentPosition - initialPosition).Magnitude > maxDistance or (tick() - startTime) > maxLifetime then
				bullet:Destroy()
				break
			end
			wait(0.1)
		end
	end)
end

-- 处理远程武器射击
function WeaponClient:Shoot()
	if not self.IsWeaponEquipped or not self.RemoteData then
		return false
	end

	-- 检查是否正在换弹
	if self.IsReloading then
		print("正在换弹，无法射击")
		return false
	end

	-- 检查弹药
	if self.CurrentAmmo <= 0 then
		print("弹药耗尽，需要换弹")
		self:Reload()
		return false
	end

	-- 检查射击冷却
	local now = tick()
	if now - self.LastShootTime < self.RemoteData.ShootCD then
		return false
	end

	-- 更新最后射击时间
	self.LastShootTime = now

	-- 获取玩家角色和相机
	local player = Players.LocalPlayer
	if not player then
		print("⚠️ 无法获取LocalPlayer")
		return false
	end

	local character = player.Character
	if not character or not character:FindFirstChild("HumanoidRootPart") then
		print("⚠️ 角色或HumanoidRootPart不存在")
		return false
	end

	local camera = workspace.CurrentCamera
	local cameraDirection = camera.CFrame.LookVector

	-- 计算子弹发射位置
	-- 计算子弹发射位置（基于 Handle 并添加偏移）
	local barrelPosition
	if self.CurrentWeapon and self.CurrentWeapon:IsA("Tool") then
		-- 只使用 Handle 作为发射点，并添加偏移
		local handle = self.CurrentWeapon:FindFirstChild("Handle")
		if handle and handle:IsA("BasePart") then
			-- 获取 Handle 的朝向（向前和向上的方向）
			local forwardDir = handle.CFrame.LookVector  -- 武器向前的方向
			local upDir = handle.CFrame.UpVector         -- 武器向上的方向

			-- 设置偏移量：往前2单位，往上1单位（可根据需要调整数值）
			local forwardOffset = 2  -- 往前的距离（越大越靠前）
			local upOffset = 0.5      -- 往上的距离（越大越靠上）

			-- 计算最终发射位置：Handle位置 + 前向偏移 + 向上偏移
			barrelPosition = handle.Position 
				+ forwardDir * forwardOffset  -- 往前偏移
				+ upDir * upOffset            -- 往上偏移

			print("从 Handle 偏移位置发射子弹: " .. tostring(barrelPosition))
		else
			-- 如果找不到 Handle，回退到角色位置（保持原有逻辑）
			barrelPosition = character.HumanoidRootPart.Position + cameraDirection * 3
			print("找不到 Handle，使用默认位置发射子弹")
		end
	else
		-- 如果没有武器引用，使用角色前方位置
		barrelPosition = character.HumanoidRootPart.Position + cameraDirection * 3
	end

	-- ===== 重要改进：计算"收敛射线"，使子弹从枪口发射但朝向准星 =====
	-- 1. 使用射线检测找到实际瞄准目标，而不是固定距离
	local maxAimDistance = self.RemoteData.Range or 100  -- 使用武器射程作为最大瞄准距离
	local aimPoint

	-- 创建射线检测参数
	local raycastParams = RaycastParams.new()
	raycastParams.FilterType = Enum.RaycastFilterType.Exclude
	raycastParams.FilterDescendantsInstances = {character} -- 排除玩家自身

	-- 从相机位置向前发射射线
	local rayResult = workspace:Raycast(camera.CFrame.Position, cameraDirection * maxAimDistance, raycastParams)

	if rayResult then
		-- 如果射线命中了物体，使用命中点作为瞄准目标
		aimPoint = rayResult.Position
	else
		-- 如果没有命中，使用最大距离的点
		aimPoint = camera.CFrame.Position + cameraDirection * maxAimDistance
	end

	-- 2. 计算从枪口到瞄准目标的方向向量
	local bulletDirection = (aimPoint - barrelPosition).Unit

	-- 3. 额外检查：确保子弹方向合理（防止向后射击等异常情况）
	local forwardDot = bulletDirection:Dot(cameraDirection)
	if forwardDot < 0.1 then -- 如果子弹方向与相机方向夹角过大，使用相机方向
		print("子弹方向异常，使用相机方向作为备用")
		bulletDirection = cameraDirection
	end

	-- 播放射击音效
	if self.RemoteData.ShootSoundEffect and self.RemoteData.ShootSoundEffect ~= "" then
		local sound = Instance.new("Sound")
		sound.SoundId = self.RemoteData.ShootSoundEffect
		sound.Volume = 1
		sound.Parent = character.HumanoidRootPart
		sound:Play()
		game:GetService("Debris"):AddItem(sound, 3)
	end

	-- 创建子弹
	local bulletModel = self.RemoteData.BallisticModel
	local bulletTemplate = nil
	local bulletFolder = ReplicatedStorage:FindFirstChild("Model")
	if bulletFolder then
		bulletFolder = bulletFolder:FindFirstChild("Equip")
		if bulletFolder then
			bulletFolder = bulletFolder:FindFirstChild("Bullet")
			if bulletFolder then
				bulletTemplate = bulletFolder:FindFirstChild(bulletModel)
			end
		end
	end

	-- 减少弹药
	self.CurrentAmmo = self.CurrentAmmo - 1
	print("发射后剩余弹药: " .. self.CurrentAmmo .. "/" .. self.RemoteData.AmmoCapacity)

	-- 获取武器实例ID
	local weaponInstanceId = nil
	if Players.LocalPlayer.Character then
		local tool = Players.LocalPlayer.Character:FindFirstChild(self.WeaponData.Name)
		if tool then
			weaponInstanceId = self:GetOrCreateWeaponInstanceId(tool)
		end
	end

	-- 先通知服务端，让服务端广播子弹创建信息给其他客户端
	-- 确保其他客户端能看到子弹
	ProtocolManager.SendMessage("PlayerShootEvent", {
		bulletModel = self.RemoteData.BallisticModel,
		position = barrelPosition,
		direction = bulletDirection,
		speed = self.RemoteData.BallisticSpeed,
		range = self.RemoteData.Range,
		damage = self.RemoteData.Damage,
		playerName = Players.LocalPlayer.Name,
		weaponId = self.WeaponData.Id,
		remoteWeaponId = self.RemoteData.Id,
		currentAmmo = self.CurrentAmmo,
		weaponInstanceId = weaponInstanceId  -- 添加武器实例ID
	})

	-- 在本地创建子弹
	if bulletTemplate then
		-- 克隆子弹模型
		local bullet = bulletTemplate:Clone()
		bullet.Parent = workspace
		bullet.Name = "Bullet_" .. Players.LocalPlayer.Name .. "_" .. tostring(now)

		-- 根据子弹类型设置物理属性
		if bullet:IsA("BasePart") then
			-- BasePart类型子弹（保持原有逻辑）
			self:SetupAdvancedBasePartBullet(bullet, barrelPosition, bulletDirection, self.RemoteData.BallisticSpeed)
		elseif bullet:IsA("Model") then
			-- Model类型子弹（新增支持）
			self:SetupAdvancedModelBullet(bullet, barrelPosition, bulletDirection, self.RemoteData.BallisticSpeed)
		else
			warn("不支持的子弹类型: " .. bullet.ClassName)
			bullet:Destroy()
		end
	else
		print("未找到子弹模型:", bulletModel)
	end

	-- 更新弹药UI
	self:UpdateAmmoUI()

	-- 如果弹药耗尽，自动换弹
	if self.CurrentAmmo <= 0 then
		self:Reload()
	end

	return true
end

-- 开始射击
function WeaponClient:StartShooting()
	if not self.IsWeaponEquipped or not self.RemoteData then
		return
	end

	-- 已经在射击状态
	if self.IsShooting then
		return
	end

	self.IsShooting = true

	-- 根据武器射击模式设置
	if self.RemoteData.WeaponType == 4 then -- 自动步枪(射击模式4)
		-- 停止之前的射击循环
		if self.ShootingLoop then
			self.ShootingLoop:Disconnect()
		end

		-- 创建新的射击循环
		self.ShootingLoop = game:GetService("RunService").Heartbeat:Connect(function()
			self:Shoot()
		end)
	else -- 点射武器(射击模式3或其他)
		self:Shoot()
		-- 点射武器只发射一次，然后自动停止射击状态
		self.IsShooting = false
	end
end

-- 停止射击
function WeaponClient:StopShooting()
	self.IsShooting = false

	-- 停止射击循环
	if self.ShootingLoop then
		self.ShootingLoop:Disconnect()
		self.ShootingLoop = nil
	end
end

-- 换弹函数
function WeaponClient:Reload()
	if not self.IsWeaponEquipped then
		print("未装备武器，无法换弹")
		return
	end

	if not self.RemoteData then
		print("不是远程武器，无法换弹")
		return
	end

	-- 检查是否已经在换弹
	if self.IsReloading then
		print("正在换弹中")
		return
	end

	-- 检查弹药是否已满
	if self.CurrentAmmo >= self.RemoteData.AmmoCapacity then
		print("弹药已满")
		return
	end

	print("开始换弹 - 武器ID: " .. self.WeaponData.Id .. ", 远程武器ID: " .. self.RemoteData.Id .. ", 射击模式: " .. self.RemoteData.WeaponType)

	-- 停止射击
	self:StopShooting()

	-- 开始换弹
	self.IsReloading = true

	-- 播放换弹音效
	local reloadSound = nil
	if self.RemoteData.SwapSoundEffect and self.RemoteData.SwapSoundEffect ~= "" then
		reloadSound = Instance.new("Sound")
		reloadSound.SoundId = self.RemoteData.SwapSoundEffect
		reloadSound.Volume = 1
		reloadSound.Name = "ReloadSound"

		-- 确保角色存在
		if Players.LocalPlayer.Character and Players.LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
			reloadSound.Parent = Players.LocalPlayer.Character.HumanoidRootPart
			reloadSound:Play()

			-- 监听音效结束，如果换弹状态仍然存在但超时，自动取消换弹状态
			spawn(function()
				-- 等待音效播放完毕再加一点缓冲时间
				local duration = reloadSound.TimeLength
				if not duration or duration <= 0 then
					duration = self.RemoteData.ReloadTime or 2
				end

				wait(duration + 1) -- 额外等待1秒作为缓冲

				-- 如果仍在换弹状态，可能是出现了问题，强制重置
				if self.IsReloading and self.WeaponData and self.WeaponData.Id == self.WeaponData.Id then
					print("换弹状态超时，强制重置")
					self.IsReloading = false

					-- 通知服务端
					ProtocolManager.SendMessage("ReloadCancelledEvent", {
						weaponName = self.WeaponData.Name
					})
				end
			end)
		end
	end

	-- 通知服务端
	ProtocolManager.SendMessage("PlayerReloadEvent", {
		weaponId = self.WeaponData.Id,
		remoteWeaponId = self.RemoteData.Id,
		currentAmmo = self.CurrentAmmo
	})

	print("已发送换弹请求到服务器")

	-- 不再在客户端更新弹药数量，完全依赖服务器通知
	-- 服务器会在换弹完成时发送ReloadFinished事件
	-- 我们已经在Initialize函数中注册了对应的事件处理
end

-- 设置高级BasePart子弹（保持原有精确逻辑）
function WeaponClient:SetupAdvancedBasePartBullet(bullet, barrelPosition, bulletDirection, bulletSpeed)
	-- 使用收敛射线方向设置子弹朝向
	bullet.CFrame = CFrame.new(barrelPosition, barrelPosition + bulletDirection)

	-- 禁用碰撞检测防止物理干扰
	bullet.CanCollide = false

	-- 设置为无实体，让射线检测可以穿透
	bullet.CanQuery = false

	-- 关闭锚定，必须为false才能使用线性速度
	bullet.Anchored = false

	-- 强制设置为无质量，确保不受重力影响
	bullet.Massless = true

	-- 设置自定义物理属性（全部为0，关闭物理模拟）
	if typeof(PhysicalProperties) == "function" then
		bullet.CustomPhysicalProperties = PhysicalProperties.new(0.0001, 0, 0, 0, 0)
	end

	-- 使用收敛射线方向设置子弹速度
	local velocityVector = bulletDirection * bulletSpeed
	bullet.AssemblyLinearVelocity = velocityVector

	-- 创建一个循环来持续校正子弹方向，确保它沿直线飞行
	local bulletController = coroutine.wrap(function()
		local startTime = tick()
		local initialPosition = barrelPosition
		local maxDistance = self.RemoteData.Range
		local maxLifetime = maxDistance / bulletSpeed

		while bullet and bullet.Parent do
			-- 检查是否超过最大飞行时间
			if (tick() - startTime) > maxLifetime then
				if bullet and bullet.Parent then
					bullet:Destroy()
				end
				break
			end

			-- 校正子弹朝向和速度
			if bullet and bullet.Parent then
				-- 重新设置线性速度，确保始终沿直线飞行
				bullet.AssemblyLinearVelocity = velocityVector

				-- 检查是否超出最大距离
				if (bullet.Position - initialPosition).Magnitude > maxDistance then
					bullet:Destroy()
					break
				end
			else
				break
			end

			-- 以较高频率更新，确保子弹保持直线
			task.wait(0.01)
		end
	end)

	-- 启动控制器
	bulletController()

	-- 子弹碰撞检测
	self:SetupBulletCollisionDetection(bullet, "BasePart")
end

-- 设置高级Model子弹（新增方法）
function WeaponClient:SetupAdvancedModelBullet(bullet, barrelPosition, bulletDirection, bulletSpeed)
	-- 确保Model有PrimaryPart
	if not bullet.PrimaryPart then
		local mainPart = bullet:FindFirstChild("Core") or
			bullet:FindFirstChild("HumanoidRootPart") or
			bullet:FindFirstChild("Torso") or
			bullet:FindFirstChildOfClass("BasePart")

		if mainPart then
			bullet.PrimaryPart = mainPart
			print("为Model子弹设置PrimaryPart: " .. mainPart.Name)
		else
			warn("Model类型子弹缺少PrimaryPart: " .. bullet.Name)
			bullet:Destroy()
			return
		end
	end

	local primaryPart = bullet.PrimaryPart

	-- 设置初始位置和方向
	bullet:SetPrimaryPartCFrame(CFrame.new(barrelPosition, barrelPosition + bulletDirection))

	-- 设置物理属性
	primaryPart.CanCollide = false
	primaryPart.CanQuery = false
	primaryPart.Anchored = false
	primaryPart.Massless = true

	-- 为Model中的所有Part设置物理属性并创建连接
	for _, part in ipairs(bullet:GetDescendants()) do
		if part:IsA("BasePart") and part ~= primaryPart then
			part.CanCollide = false
			part.CanQuery = false
			part.Massless = true
			part.Anchored = false  -- 确保子部件不被锚定

			-- 创建WeldConstraint将子部件连接到PrimaryPart
			local weld = Instance.new("WeldConstraint")
			weld.Part0 = primaryPart
			weld.Part1 = part
			weld.Parent = primaryPart

			print("已将 " .. part.Name .. " 连接到 " .. primaryPart.Name)
		end
	end

	-- 设置自定义物理属性
	if typeof(PhysicalProperties) == "function" then
		primaryPart.CustomPhysicalProperties = PhysicalProperties.new(0.0001, 0, 0, 0, 0)
	end

	-- 使用收敛射线方向设置子弹速度
	local velocityVector = bulletDirection * bulletSpeed
	primaryPart.AssemblyLinearVelocity = velocityVector

	-- 创建Model子弹控制器
	local bulletController = coroutine.wrap(function()
		local startTime = tick()
		local initialPosition = barrelPosition
		local maxDistance = self.RemoteData.Range
		local maxLifetime = maxDistance / bulletSpeed

		while bullet and bullet.Parent and bullet.PrimaryPart do
			-- 检查是否超过最大飞行时间
			if (tick() - startTime) > maxLifetime then
				if bullet and bullet.Parent then
					bullet:Destroy()
				end
				break
			end

			-- 校正子弹朝向和速度
			if bullet and bullet.Parent and bullet.PrimaryPart then
				-- 重新设置线性速度，确保始终沿直线飞行
				bullet.PrimaryPart.AssemblyLinearVelocity = velocityVector

				-- 检查是否超出最大距离
				if (bullet.PrimaryPart.Position - initialPosition).Magnitude > maxDistance then
					bullet:Destroy()
					break
				end
			else
				break
			end

			-- 以较高频率更新，确保子弹保持直线
			task.wait(0.01)
		end
	end)

	-- 启动控制器
	bulletController()

	-- Model子弹碰撞检测
	self:SetupBulletCollisionDetection(bullet, "Model")
end

-- 设置子弹碰撞检测（统一方法）
function WeaponClient:SetupBulletCollisionDetection(bullet, bulletType)
	local function handleBulletCollision(hit)
		if not bullet or not bullet.Parent then return end

		-- 排除玩家自身
		local character = Players.LocalPlayer.Character
		if hit:IsDescendantOf(character) then
			return -- 忽略玩家自身
		end

		-- 排除子弹自身的部件（对Model类型重要）
		if bulletType == "Model" and hit:IsDescendantOf(bullet) then
			return
		end

		-- 检查是否命中monster文件夹中的模型
		local monsterFolder = workspace:FindFirstChild("monster")
		if monsterFolder and hit:IsDescendantOf(monsterFolder) then
			-- 找到怪物模型（取最顶层模型）
			local targetModel = hit:FindFirstAncestorOfClass("Model")
			if targetModel then
				-- 发送命中事件到服务器
				local bulletPosition = bulletType == "Model" and bullet.PrimaryPart.Position or bullet.Position
				ProtocolManager.SendMessage("BulletHitEvent", {
					targetName = targetModel.Name, -- 怪物模型名称
					position = bulletPosition,
					targetPosition = targetModel.PrimaryPart and targetModel.PrimaryPart.Position or bulletPosition,
					damage = self.RemoteData.Damage -- 武器伤害
				})
				-- 销毁子弹
				bullet:Destroy()
				return
			end
		end

		-- 确保不是玩家自己或已经销毁
		if hit and hit.Parent and hit.Parent ~= character and hit.Parent.Parent ~= character then
			-- 尝试查找命中的角色
			local hitModel = hit:FindFirstAncestorOfClass("Model")
			if hitModel then
				local humanoid = hitModel:FindFirstChildOfClass("Humanoid")
				if humanoid and humanoid.Health > 0 then
					-- 获取目标的位置信息用于精确识别
					local targetPosition = nil
					local humanoidRootPart = hitModel:FindFirstChild("HumanoidRootPart")
					if humanoidRootPart then
						targetPosition = humanoidRootPart.Position
					elseif hitModel.PrimaryPart then
						targetPosition = hitModel.PrimaryPart.Position
					else
						-- 如果没有HumanoidRootPart或PrimaryPart，使用命中点
						local bulletPosition = bulletType == "Model" and bullet.PrimaryPart.Position or bullet.Position
						targetPosition = bulletPosition
					end

					-- 通知服务端处理伤害
					local bulletPosition = bulletType == "Model" and bullet.PrimaryPart.Position or bullet.Position
					print("子弹命中: " .. hitModel.Name .. " 位置: " .. tostring(targetPosition))
					ProtocolManager.SendMessage("BulletHitEvent", {
						targetName = hitModel.Name,
						targetUserId = hitModel:GetAttribute("UserId"),
						position = bulletPosition, -- 子弹命中位置
						targetPosition = targetPosition, -- 目标中心位置
						weaponId = self.WeaponData.Id,
						remoteWeaponId = self.RemoteData.Id,
						damage = self.RemoteData.Damage
					})

					-- 销毁子弹
					bullet:Destroy()
					return
				end
			end

			-- 命中了非角色物体，仍然销毁子弹
			print("子弹命中物体")
			bullet:Destroy()
		end
	end

	-- 根据子弹类型设置碰撞检测
	if bulletType == "BasePart" then
		bullet.Touched:Connect(handleBulletCollision)
	elseif bulletType == "Model" then
		-- 为Model中的所有Part设置碰撞检测
		for _, part in ipairs(bullet:GetDescendants()) do
			if part:IsA("BasePart") then
				part.Touched:Connect(handleBulletCollision)
			end
		end
	end
end

-- 设置网络同步的BasePart子弹（用于接收其他玩家的子弹）
function WeaponClient:SetupNetworkBasePartBullet(bullet, data)
	-- 初始位置和方向设置
	bullet.CFrame = CFrame.new(data.startPosition, data.startPosition + data.direction)
	bullet.CanCollide = false
	bullet.CanQuery = false
	bullet.Anchored = false
	bullet.Massless = true

	-- 设置自定义物理属性（全部为0，关闭物理模拟）
	if typeof(PhysicalProperties) == "function" then
		bullet.CustomPhysicalProperties = PhysicalProperties.new(0.0001, 0, 0, 0, 0)
	end

	-- 创建轨迹效果
	local attachment0 = Instance.new("Attachment")
	attachment0.Parent = bullet
	attachment0.Position = Vector3.new(-0.5, 0, 0)

	local attachment1 = Instance.new("Attachment")
	attachment1.Parent = bullet
	attachment1.Position = Vector3.new(0.5, 0, 0)

	local trail = Instance.new("Trail")
	trail.Attachment0 = attachment0
	trail.Attachment1 = attachment1
	trail.Lifetime = 0.2
	trail.MinLength = 0.1
	trail.MaxLength = 20
	trail.Transparency = NumberSequence.new({
		NumberSequenceKeypoint.new(0, 0),
		NumberSequenceKeypoint.new(1, 1)
	})
	trail.Color = ColorSequence.new(Color3.new(1, 0.7, 0))
	trail.WidthScale = NumberSequence.new(1)
	trail.Parent = bullet

	-- 光效
	local light = Instance.new("PointLight")
	light.Brightness = 1
	light.Color = Color3.new(1, 0.7, 0)
	light.Range = 4
	light.Parent = bullet

	-- 使用AssemblyLinearVelocity替代BodyVelocity，更精确地控制子弹
	local velocityVector = data.direction.Unit * data.speed
	bullet.AssemblyLinearVelocity = velocityVector

	-- 创建一个循环来持续校正子弹方向，确保它沿直线飞行
	local bulletController = coroutine.wrap(function()
		local startTime = tick()
		local initialPosition = data.startPosition
		local maxDistance = data.range or 100
		local maxLifetime = maxDistance / data.speed

		while bullet and bullet.Parent do
			-- 检查是否超过最大飞行时间
			if (tick() - startTime) > maxLifetime then
				if bullet and bullet.Parent then
					bullet:Destroy()
				end
				break
			end

			-- 校正子弹朝向和速度
			if bullet and bullet.Parent then
				-- 重新设置线性速度，确保始终沿直线飞行
				bullet.AssemblyLinearVelocity = velocityVector
				bullet.CFrame = CFrame.new(bullet.Position, bullet.Position + data.direction)

				-- 检查是否超出最大距离
				if (bullet.Position - initialPosition).Magnitude > maxDistance then
					bullet:Destroy()
					break
				end
			else
				break
			end

			-- 以较高频率更新，确保子弹保持直线
			task.wait(0.01)
		end
	end)

	-- 启动控制器
	bulletController()

	-- 子弹碰撞检测
	bullet.Touched:Connect(function(hit)
		if not bullet or not bullet.Parent then return end

		-- 确保不是玩家自己
		if hit and hit.Parent then
			local character = workspace:FindFirstChild(data.playerName)
			if hit.Parent ~= character and hit.Parent.Parent ~= character then
				-- 创建子弹命中特效
				local hitEffect = Instance.new("Part")
				hitEffect.Size = Vector3.new(0.5, 0.5, 0.5)
				hitEffect.Anchored = true
				hitEffect.CanCollide = false
				hitEffect.Position = bullet.Position
				hitEffect.Material = Enum.Material.Neon
				hitEffect.BrickColor = BrickColor.new("Bright yellow")
				hitEffect.Shape = Enum.PartType.Ball
				hitEffect.Parent = workspace

				-- 创建爆炸光效
				local explosionLight = Instance.new("PointLight")
				explosionLight.Brightness = 5
				explosionLight.Color = Color3.new(1, 0.7, 0)
				explosionLight.Range = 8
				explosionLight.Parent = hitEffect

				-- 淡出特效
				game:GetService("Debris"):AddItem(hitEffect, 0.3)

				-- 销毁子弹
				bullet:Destroy()
			end
		end
	end)
end

-- 设置网络同步的Model子弹（用于接收其他玩家的子弹）
function WeaponClient:SetupNetworkModelBullet(bullet, data)
	-- 确保Model有PrimaryPart
	if not bullet.PrimaryPart then
		local mainPart = bullet:FindFirstChild("Core") or
			bullet:FindFirstChild("HumanoidRootPart") or
			bullet:FindFirstChild("Torso") or
			bullet:FindFirstChildOfClass("BasePart")

		if mainPart then
			bullet.PrimaryPart = mainPart
			print("为网络Model子弹设置PrimaryPart: " .. mainPart.Name)
		else
			warn("网络Model类型子弹缺少PrimaryPart: " .. bullet.Name)
			bullet:Destroy()
			return
		end
	end

	local primaryPart = bullet.PrimaryPart

	-- 设置初始位置和方向
	bullet:SetPrimaryPartCFrame(CFrame.new(data.startPosition, data.startPosition + data.direction))

	-- 设置物理属性
	primaryPart.CanCollide = false
	primaryPart.CanQuery = false
	primaryPart.Anchored = false
	primaryPart.Massless = true

	-- 为Model中的所有Part设置物理属性并创建连接
	for _, part in ipairs(bullet:GetDescendants()) do
		if part:IsA("BasePart") and part ~= primaryPart then
			part.CanCollide = false
			part.CanQuery = false
			part.Massless = true
			part.Anchored = false  -- 确保子部件不被锚定

			-- 创建WeldConstraint将子部件连接到PrimaryPart
			local weld = Instance.new("WeldConstraint")
			weld.Part0 = primaryPart
			weld.Part1 = part
			weld.Parent = primaryPart

			print("网络子弹：已将 " .. part.Name .. " 连接到 " .. primaryPart.Name)
		end
	end

	-- 设置自定义物理属性
	if typeof(PhysicalProperties) == "function" then
		primaryPart.CustomPhysicalProperties = PhysicalProperties.new(0.0001, 0, 0, 0, 0)
	end

	-- 使用收敛射线方向设置子弹速度
	local velocityVector = data.direction.Unit * data.speed
	primaryPart.AssemblyLinearVelocity = velocityVector

	-- 创建Model子弹控制器
	local bulletController = coroutine.wrap(function()
		local startTime = tick()
		local initialPosition = data.startPosition
		local maxDistance = data.range or 100
		local maxLifetime = maxDistance / data.speed

		while bullet and bullet.Parent and bullet.PrimaryPart do
			-- 检查是否超过最大飞行时间
			if (tick() - startTime) > maxLifetime then
				if bullet and bullet.Parent then
					bullet:Destroy()
				end
				break
			end

			-- 校正子弹朝向和速度
			if bullet and bullet.Parent and bullet.PrimaryPart then
				-- 重新设置线性速度，确保始终沿直线飞行
				bullet.PrimaryPart.AssemblyLinearVelocity = velocityVector

				-- 检查是否超出最大距离
				if (bullet.PrimaryPart.Position - initialPosition).Magnitude > maxDistance then
					bullet:Destroy()
					break
				end
			else
				break
			end

			-- 以较高频率更新，确保子弹保持直线
			task.wait(0.01)
		end
	end)

	-- 启动控制器
	bulletController()

	-- Model子弹碰撞检测
	local function handleModelBulletCollision(hit)
		if not bullet or not bullet.Parent then return end

		-- 排除子弹自身的部件（对Model类型重要）
		if hit:IsDescendantOf(bullet) then
			return
		end

		-- 确保不是发射玩家自己
		if hit and hit.Parent then
			local character = workspace:FindFirstChild(data.playerName)
			if hit.Parent ~= character and hit.Parent.Parent ~= character then
				-- 创建子弹命中特效
				local hitEffect = Instance.new("Part")
				hitEffect.Size = Vector3.new(0.5, 0.5, 0.5)
				hitEffect.Anchored = true
				hitEffect.CanCollide = false
				hitEffect.Position = bullet.PrimaryPart.Position
				hitEffect.Material = Enum.Material.Neon
				hitEffect.BrickColor = BrickColor.new("Bright yellow")
				hitEffect.Shape = Enum.PartType.Ball
				hitEffect.Parent = workspace

				-- 创建爆炸光效
				local explosionLight = Instance.new("PointLight")
				explosionLight.Brightness = 5
				explosionLight.Color = Color3.new(1, 0.7, 0)
				explosionLight.Range = 8
				explosionLight.Parent = hitEffect

				-- 淡出特效
				game:GetService("Debris"):AddItem(hitEffect, 0.3)

				-- 销毁子弹
				bullet:Destroy()
			end
		end
	end

	-- 为Model中的所有Part设置碰撞检测
	for _, part in ipairs(bullet:GetDescendants()) do
		if part:IsA("BasePart") then
			part.Touched:Connect(handleModelBulletCollision)
		end
	end
end

return WeaponClient