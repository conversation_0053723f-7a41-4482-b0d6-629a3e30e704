--!strict
-- 状态配置文件，定义装备对玩家属性的影响
export type StateConfig = {
  Id: number,
  Name: string,
  Health: number,
  MoveSpeed: number,
  Attack: number,
  Description: string
}

local StateConfig: {StateConfig} = {
  -- 基础装备状态
  {Id=1001, Name="轻型护甲", Health=20, MoveSpeed=2, Attack=0, Description="提供基础防护，轻微提升移动速度"},
  {Id=1002, Name="重型护甲", Health=50, MoveSpeed=-2, Attack=5, Description="提供强力防护，但会降低移动速度"},
  {Id=1003, Name="敏捷靴", Health=0, MoveSpeed=5, Attack=0, Description="大幅提升移动速度"},
  {Id=1004, Name="力量手套", Health=0, MoveSpeed=0, Attack=10, Description="大幅提升攻击力"},
  {Id=1005, Name="平衡套装", Health=15, MoveSpeed=3, Attack=3, Description="平衡提升所有属性"},
  
  -- 高级装备状态
  {Id=2001, Name="疾风斗篷", Health=10, MoveSpeed=8, Attack=2, Description="极大提升移动速度"},
  {Id=2002, Name="钢铁护甲", Health=80, MoveSpeed=-5, Attack=8, Description="最强防护，但严重影响移动"},
  {Id=2003, Name="战士之力", Health=30, MoveSpeed=1, Attack=15, Description="战士专用，大幅提升攻击力"},
  
  -- 特殊状态
  {Id=3001, Name="临时加速", Health=0, MoveSpeed=10, Attack=0, Description="临时状态：极速移动"},
  {Id=3002, Name="虚弱状态", Health=-20, MoveSpeed=-5, Attack=-5, Description="负面状态：全属性下降"},
}

return StateConfig
