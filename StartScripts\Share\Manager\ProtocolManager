--客户端调用  客户端向服务端发消息
local replicatedStorage=game:GetService("ReplicatedStorage")
local player=game:GetService("Players")

local NotifyManager=require(replicatedStorage.Scripts.Share.Manager.NotifyManager)

local SendMaxevent=replicatedStorage.Remotes.SendMaxData
local SendPlayerRemove=replicatedStorage.Remotes.SendPlayerRemove

local ProtocolManager = {}

ProtocolManager.remoteFolder=replicatedStorage.Remotes


ProtocolManager.Protocols={

	CreateRoom={Name="CreateRoom",RemoteType="RemoteEvent"},
	RemoveRoom1={Name="RemoveRoom1",RemoteType="RemoteEvent"},
	AddRoom={Name="AddRoom",RemoteType="RemoteEvent"},
	StartTime={Name="StartTime",RemoteType="RemoteEvent"},
	TimeOver={Name="TimeOver",RemoteType="RemoteEvent"},
	IsLoad={Name="IsLoad",RemoteType="RemoteEvent"},
	SwitchProfessionEvent = {Name="SwitchProfessionEvent",RemoteType="RemoteEvent"},
	CanLoad={Name="CanLoad",RemoteType="RemoteEvent"},
	TeleportToGame={Name="TeleportToGame",RemoteType="RemoteEvent"}
}

ProtocolManager.clientCall={}


--服务端调用 生成所有的RemoteEvent和RemoteFunction
function ProtocolManager.GenerateRemote()
	for k,v in pairs(ProtocolManager.Protocols) do
		if v.RemoteType=="RemoteEvent" then--客户端调用服务器无返回值方法(RemoteEvent)
			local remoteEvent=Instance.new("RemoteEvent")
			remoteEvent.Name=k
			remoteEvent.Parent=ProtocolManager.remoteFolder
			remoteEvent.OnServerEvent:Connect(function(player,message)
				ProtocolManager[k](player,message)
			end)
		elseif v.RemoteType=="RemoteFunction" then--客户端调用服务器有返回值方法(RemoteFunction)
			local remoteFunction=Instance.new("RemoteFunction")
			remoteFunction.Name=k
			remoteFunction.Parent=ProtocolManager.remoteFolder
			remoteFunction.OnServerInvoke=function(player,message)
				return ProtocolManager[k](player,message)
			end
		end
	end
end

--客户端调用 初始化客户端调用服务端的Remote事件
function ProtocolManager.InitClientProtocol()
	local tempRemotes=replicatedStorage.Remotes:GetChildren()

	for k,v in pairs(tempRemotes) do
		if ProtocolManager.Protocols[v.Name] then
			if ProtocolManager.Protocols[v.Name].RemoteType=="RemoteEvent" then
				--v为RemoteEvent
				ProtocolManager.clientCall[v.Name]=function (message)
					v:FireServer(message)
				end
			elseif ProtocolManager.Protocols[v.Name].RemoteType=="RemoteFunction" then
				--v为RemoteFunction
				ProtocolManager.clientCall[v.Name]=function (message)
					return v:InvokeServer(message)
				end
			end
		end
	end
end

--客户端调用  向服务端发送请求
function ProtocolManager.SendMessage(protocolName,data)
	
	if ProtocolManager.clientCall[protocolName] then
		print(protocolName)
		return ProtocolManager.clientCall[protocolName](data)
	end
end



function ProtocolManager.CreateRoom(player,data)
	print("创造房间")
	SendMaxevent:Fire(player,data)
end

function ProtocolManager.RemoveRoom1(player,maxPlayer)
	print("移除")
	SendPlayerRemove:Fire(player,maxPlayer)
end
function ProtocolManager.SwitchProfessionEvent(player,data)
	local currentPlayer = data.player
	local model = data.professionModel
	local Id = data.Id
	local price = data.price
	local profession = data.profession
	local ProfessionManager =require(replicatedStorage.Scripts.Server.Manager.ProfessionManager)
	if ProfessionManager then
		ProfessionManager.changeModel(currentPlayer, model,Id,price,profession)		
	else
		warn("无法找到ProfessionSwitch")
	end
end


function ProtocolManager.StartTime(player)
	
end

function ProtocolManager.TimeOver()
	print("准备传送")
	-- 获取所有在线玩家
	local Players = game:GetService("Players")
	local allPlayers = Players:GetPlayers()

	if #allPlayers > 0 then
		-- 调用传送管理器
		local TeleportManager = require(replicatedStorage.Scripts.Server.Manager.TeleportManager)
		TeleportManager:teleportPlayers(allPlayers)
	else
		warn("没有玩家可传送")
	end
end

function ProtocolManager.IsLoad(player,data)
	NotifyManager.FireAllClient("IsLoad",data)
end

-- 手动触发传送到游戏场景
function ProtocolManager.TeleportToGame(player, data)
	print("玩家 " .. player.Name .. " 请求传送到游戏场景")

	-- 检查玩家是否已选择职业
	local ProfessionManager = require(replicatedStorage.Scripts.Server.Manager.ProfessionManager)
	local userId = player.UserId
	local playerProfession = ProfessionManager.playerProfession[userId]
	local playerProfessionId = ProfessionManager.playerProfessionState[userId]

	if not playerProfession or not playerProfessionId then
		warn("玩家 " .. player.Name .. " 尚未选择职业，无法传送")
		-- 可以发送通知给客户端
		NotifyManager.FireClient("TeleportError", player, {
			message = "请先选择职业再进入游戏"
		})
		return
	end

	-- 执行传送
	local TeleportManager = require(replicatedStorage.Scripts.Server.Manager.TeleportManager)
	TeleportManager:teleportPlayers({player})
end


return ProtocolManager

