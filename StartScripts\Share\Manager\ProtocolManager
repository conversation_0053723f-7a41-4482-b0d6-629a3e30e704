--客户端调用  客户端向服务端发消息
local replicatedStorage=game:GetService("ReplicatedStorage")
local player=game:GetService("Players")

local NotifyManager=require(replicatedStorage.Scripts.Share.Manager.NotifyManager)

local SendMaxevent=replicatedStorage.Remotes.SendMaxData
local SendPlayerRemove=replicatedStorage.Remotes.SendPlayerRemove

local ProtocolManager = {}

ProtocolManager.remoteFolder=replicatedStorage.Remotes


ProtocolManager.Protocols={
	
	CreateRoom={Name="CreateRoom",RemoteType="RemoteEvent"},
	RemoveRoom1={Name="RemoveRoom1",RemoteType="RemoteEvent"},
	AddRoom={Name="AddRoom",RemoteType="RemoteEvent"},
	StartTime={Name="StartTime",RemoteType="RemoteEvent"},
	TimeOver={Name="TimeOver",RemoteType="RemoteEvent"},
	IsLoad={Name="IsLoad",RemoteType="RemoteEvent"},
	SwitchProfessionEvent = {Name="SwitchProfessionEvent",RemoteType="RemoteEvent"},
	CanLoad={Name="CanLoad",RemoteType="RemoteEvent"}
}

ProtocolManager.clientCall={}


--服务端调用 生成所有的RemoteEvent和RemoteFunction
function ProtocolManager.GenerateRemote()
	for k,v in pairs(ProtocolManager.Protocols) do
		if v.RemoteType=="RemoteEvent" then--客户端调用服务器无返回值方法(RemoteEvent)
			local remoteEvent=Instance.new("RemoteEvent")
			remoteEvent.Name=k
			remoteEvent.Parent=ProtocolManager.remoteFolder
			remoteEvent.OnServerEvent:Connect(function(player,message)
				ProtocolManager[k](player,message)
			end)
		elseif v.RemoteType=="RemoteFunction" then--客户端调用服务器有返回值方法(RemoteFunction)
			local remoteFunction=Instance.new("RemoteFunction")
			remoteFunction.Name=k
			remoteFunction.Parent=ProtocolManager.remoteFolder
			remoteFunction.OnServerInvoke=function(player,message)
				return ProtocolManager[k](player,message)
			end
		end
	end
end

--客户端调用 初始化客户端调用服务端的Remote事件
function ProtocolManager.InitClientProtocol()
	local tempRemotes=replicatedStorage.Remotes:GetChildren()

	for k,v in pairs(tempRemotes) do
		if ProtocolManager.Protocols[v.Name] then
			if ProtocolManager.Protocols[v.Name].RemoteType=="RemoteEvent" then
				--v为RemoteEvent
				ProtocolManager.clientCall[v.Name]=function (message)
					v:FireServer(message)
				end
			elseif ProtocolManager.Protocols[v.Name].RemoteType=="RemoteFunction" then
				--v为RemoteFunction
				ProtocolManager.clientCall[v.Name]=function (message)
					return v:InvokeServer(message)
				end
			end
		end
	end
end

--客户端调用  向服务端发送请求
function ProtocolManager.SendMessage(protocolName,data)
	
	if ProtocolManager.clientCall[protocolName] then
		print(protocolName)
		return ProtocolManager.clientCall[protocolName](data)
	end
end



function ProtocolManager.CreateRoom(player,data)
	print("创造房间")
	SendMaxevent:Fire(player,data)
end

function ProtocolManager.RemoveRoom1(player,maxPlayer)
	print("移除")
	SendPlayerRemove:Fire(player,maxPlayer)
end
function ProtocolManager.SwitchProfessionEvent(player,data)
	local currentPlayer = data.player
	local model = data.professionModel
	local Id = data.Id
	local price = data.price
	local profession = data.profession
	local ProfessionManager =require(replicatedStorage.Scripts.Server.Manager.ProfessionManager)
	if ProfessionManager then
		ProfessionManager.changeModel(currentPlayer, model,Id,price,profession)		
	else
		warn("无法找到ProfessionSwitch")
	end
end


function ProtocolManager.StartTime(player)
	
end

function ProtocolManager.TimeOver()
	print("准备传送")
end

function ProtocolManager.IsLoad(player,data)
	NotifyManager.FireAllClient("IsLoad",data)
end


return ProtocolManager

