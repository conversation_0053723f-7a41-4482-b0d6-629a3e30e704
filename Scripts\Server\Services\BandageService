-- 服务端代码 (BandageService.lua)
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local PlayerDownedService = require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)
local PlayerAttributeManager = require(ReplicatedStorage.Scripts.Server.Services.PlayerAttributeManager)

local BandageService = {}

-- 绷带治疗量
BandageService.HealAmount = 30
-- 使用绷带时间
BandageService.UseTime = 3.7

-- 记录正在使用绷带的玩家
local playersUsingBandage = {}

-- 记录绷带使用开始时间（用于验证使用时长）
local bandageStartTimes = {}

-- 初始化服务
function BandageService:Initialize()
	print("BandageService 初始化完成")
end

-- 开始使用绷带（保存属性并应用使用状态）
function BandageService:StartBandageUse(player)
	if not player then return false end

	-- 检查是否已经在使用绷带
	if playersUsingBandage[player] then
		print(player.Name .. " 已经在使用绷带")
		return false
	end

	-- 保存当前属性并应用绷带使用状态
	local attributeSaved = PlayerAttributeManager:StartBandageUse(player)
	if attributeSaved then
		playersUsingBandage[player] = true
		bandageStartTimes[player] = tick()
		print("开始绷带使用 - 玩家:", player.Name, "已保存属性并降低速度")

		-- 通知客户端绷带使用开始
		NotifyService.FireClient("BandageUseStarted", player, {
			walkSpeed = 3
		})

		return true
	else
		warn("保存", player.Name, "的绷带使用属性失败")
		return false
	end
end

-- 结束使用绷带（恢复属性）
function BandageService:EndBandageUse(player, completed)
	if not player then return false end

	-- 检查是否在使用绷带
	if not playersUsingBandage[player] then
		print(player.Name .. " 没有在使用绷带")
		return false
	end

	-- 恢复绷带使用前的属性
	local attributeRestored = PlayerAttributeManager:EndBandageUse(player)
	if attributeRestored then
		print("结束绷带使用 - 玩家:", player.Name, "已恢复属性")

		-- 通知客户端绷带使用结束
		NotifyService.FireClient("BandageUseEnded", player, {
			completed = completed or false
		})
	else
		warn("恢复", player.Name, "的绷带使用属性失败")
	end

	-- 清理状态
	playersUsingBandage[player] = nil
	bandageStartTimes[player] = nil

	return attributeRestored
end

-- 处理绷带使用完成
function BandageService:HandleUseBandage(player, data)
	if not player then return end

	-- 检查玩家是否正在使用绷带
	if not playersUsingBandage[player] then
		print(player.Name .. " 没有在使用绷带，忽略完成请求")
		return
	end

	-- 验证使用时长（防止作弊）
	local startTime = bandageStartTimes[player]
	if startTime then
		local elapsed = tick() - startTime
		if elapsed < (BandageService.UseTime - 0.5) then -- 允许0.5秒的网络延迟
			warn(player.Name .. " 绷带使用时间不足，可能存在作弊行为")
			self:EndBandageUse(player, false)
			return
		end
	end

	-- 立即执行治疗逻辑（不再延迟）
	local targetPlayer = player -- 默认是自救

	-- 支持新的targetPlayerId参数（来自RescueUIService）
	if data.targetPlayerId then
		targetPlayer = Players:GetPlayerByUserId(data.targetPlayerId)
		if not targetPlayer then
			NotifyService.FireClient("ShowMessage", player, {message = "目标玩家不存在", duration = 3})
			playersUsingBandage[player] = nil
			return
		end

		-- 非自救时，必须目标是濒死玩家
		if targetPlayer ~= player then
			local isTargetDowned = PlayerDownedService:IsPlayerDowned(targetPlayer)
			if not isTargetDowned then
				NotifyService.FireClient("ShowMessage", player, {message = "只能对濒死玩家使用绷带", duration = 3})
				playersUsingBandage[player] = nil
				return -- 拒绝使用
			end
		end
	-- 兼容旧的targetPlayer参数
	elseif data.targetPlayer then
		targetPlayer = Players:FindFirstChild(data.targetPlayer)
		if not targetPlayer then
			NotifyService.FireClient("ShowMessage", player, {message = "目标玩家不存在", duration = 3})
			playersUsingBandage[player] = nil
			return
		end

		-- 非自救时，必须目标是濒死玩家
		if targetPlayer ~= player then
			local isTargetDowned = PlayerDownedService:IsPlayerDowned(targetPlayer)
			if not isTargetDowned then
				NotifyService.FireClient("ShowMessage", player, {message = "只能对濒死玩家使用绷带", duration = 3})
				playersUsingBandage[player] = nil
				return -- 拒绝使用
			end
		end
	end

	local targetCharacter = targetPlayer.Character
	if not targetCharacter then
		NotifyService.FireClient("ShowMessage", player, {
			message = "目标角色不存在",
			duration = 3
		})
		playersUsingBandage[player] = nil
		return
	end

	local humanoid = targetCharacter:FindFirstChildOfClass("Humanoid")
	if not humanoid then
		playersUsingBandage[player] = nil
		return
	end

	-- 检查目标是否处于濒死状态
	local isDowned = PlayerDownedService:IsPlayerDowned(targetPlayer)

	-- 执行治疗或复活
	if isDowned then
		PlayerDownedService:RevivePlayer(targetPlayer)
		NotifyService.FireClient("ShowMessage", player, {
			message = "你成功复活了 " .. targetPlayer.Name,
			duration = 3
		})
		NotifyService.FireClient("ShowMessage", targetPlayer, {
			message = player.Name .. " 使用绷带复活了你",
			duration = 3
		})
		print(player.Name .. " 使用绷带复活了 " .. targetPlayer.Name)
	else
		local currentHealth = humanoid.Health
		local maxHealth = humanoid.MaxHealth
		local newHealth = math.min(currentHealth + BandageService.HealAmount, maxHealth)
		humanoid.Health = newHealth

		-- 同步实际血量到客户端（校正本地预测）
		self:SyncActualHealthToClients(targetPlayer, newHealth)

		if targetPlayer == player then
			NotifyService.FireClient("ShowMessage", player, {
				message = "你使用绷带恢复了 " .. BandageService.HealAmount .. " 点生命值",
				duration = 3
			})
		else
			NotifyService.FireClient("ShowMessage", player, {
				message = "你为 " .. targetPlayer.Name .. " 恢复了 " .. BandageService.HealAmount .. " 点生命值",
				duration = 3
			})
			NotifyService.FireClient("ShowMessage", targetPlayer, {
				message = player.Name .. " 为你恢复了 " .. BandageService.HealAmount .. " 点生命值",
				duration = 3
			})
		end

		print(player.Name .. " 使用绷带为 " .. (targetPlayer.Name or "自己") .. " 恢复了 " .. BandageService.HealAmount .. " 点生命值")
	end

	-- 移除绷带物品
	self:RemoveEquippedBandage(player)

	-- 结束绷带使用，恢复属性
	self:EndBandageUse(player, true)
end

-- 从玩家装备中移除绷带
function BandageService:RemoveEquippedBandage(player)
	if not player or not player.Character then return end

	-- 查找玩家装备中的绷带工具
	for _, item in ipairs(player.Character:GetChildren()) do
		if item:IsA("Tool") and item.Name == "Bandage" then
			item:Destroy()
			print("[BandageService] 移除装备中的绷带")
			return
		end
	end

	print("[BandageService] 未找到装备中的绷带")
end

-- 同步实际血量到客户端（校正本地预测）
function BandageService:SyncActualHealthToClients(targetPlayer, actualHealth)
	-- 发送给被救者（校正其临时状态）
	NotifyService.FireClient("SyncActualHealth", targetPlayer, {
		actualHealth = actualHealth
	})
end

-- 取消绷带使用（中途停止）
function BandageService:CancelBandageUse(player)
	if not player then return false end

	if playersUsingBandage[player] then
		print("取消绷带使用 - 玩家:", player.Name)
		self:EndBandageUse(player, false)
		return true
	end

	return false
end

-- 检查玩家是否正在使用绷带
function BandageService:IsPlayerUsingBandage(player)
	if not player then return false end
	return playersUsingBandage[player] == true
end

return BandageService