local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local ProtocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local NotifyManager 	= require(ReplicatedStorage.Scripts.Share.Manager.NotifyManager)
local ProfessionSwitch = {}
local HasProfession = {}
local professionFolder = nil
-- 重启动画系统
local function restartAnimationSystem(character)
	-- 确保Humanoid存在
	local humanoid = character:WaitForChild("Humanoid", 2)
	if not humanoid then
		warn("Humanoid not found in character")
		return
	end

	-- 重启Animate脚本
	local animateScript = character:FindFirstChild("Animate")
	if animateScript then
		animateScript.Enabled = false
		task.wait()
		animateScript.Enabled = true
	else
		warn("Animate script not found in character")
	end

	-- 确保Animator存在
	if not humanoid:FindFirstChild("Animator") then
		Instance.new("Animator").Parent = humanoid
		print("Animator created")
	end
end
-- 检查玩家是否拥有该职业
local function checkHasProfession(profession)
	for _, pro in ipairs(HasProfession) do
		if pro == profession then
			return true
		end
	end
	return false
end
-- 更新单个职业触发器的UI显示
local function updateProfessionTrigger(prompt, profession, price)
	if checkHasProfession(profession) then
		prompt.ObjectText = "Change"
		prompt.ActionText = "Change Profession"
	else
		prompt.ObjectText = "Price:"..tostring(price)
		prompt.ActionText = "Buy & Change"
	end
end
-- 刷新所有职业触发器的显示
local function refreshAllTriggers()
	if not professionFolder then return end
	for _, item in ipairs(professionFolder:GetChildren()) do
		local proximityPrompt = item:FindFirstChildOfClass("ProximityPrompt")
		local price = item:FindFirstChild("Price")
		local profession = item:FindFirstChild("Profession")

		if proximityPrompt and price and profession then
			updateProfessionTrigger(proximityPrompt, profession.Value, price.Value)
		end
	end
end

local function refreshTriggers(professionTarget)
	print(professionTarget)
	if not professionFolder then return end
	for _, item in ipairs(professionFolder:GetChildren()) do
		local proximityPrompt = item:FindFirstChildOfClass("ProximityPrompt")
		local price = item:FindFirstChild("Price")
		local profession = item:FindFirstChild("Profession") 

		if proximityPrompt and price and profession then
			if profession.Value == professionTarget then
				
				updateProfessionTrigger(proximityPrompt, professionTarget, price.Value)
			end
		end
	end
end
-- 初始化职业切换触发器
local function initProfessionTriggers()
	print("职业选择初始化")
	professionFolder = workspace:FindFirstChild("Tower"):FindFirstChild("Profession")
	if not professionFolder then
		warn("Profession folder not found in workspace")
		return
	end

	local professions = professionFolder:GetChildren()
	for _, item in ipairs(professions) do
		local proximityPrompt = item:FindFirstChildOfClass("ProximityPrompt")
		local professionModel = item:FindFirstChild("ProfessionModel")
		local Id = item:FindFirstChild("Id")
		local price = item:FindFirstChild("Price")
		local profession = item:FindFirstChild("Profession")
		if proximityPrompt and professionModel and Id and price and profession then
			local idNum = Id.Value 
			local priceNum = price.Value
			local professionStr = profession.Value

			-- 初始化提示显示
			updateProfessionTrigger(proximityPrompt, professionStr, priceNum)

			-- 绑定触发事件
			proximityPrompt.Triggered:Connect(function(player)
				if idNum and priceNum and professionStr then
					ProtocolManager.SendMessage("SwitchProfessionEvent", {
						player = player,
						professionModel = professionModel,
						Id = idNum,
						price = checkHasProfession(professionStr) and 0 or priceNum,
						profession = professionStr
					})
				end
			end)
		end
	end
end

-- 处理角色变更，更新相机和动画
local function handleCharacterChange(character)
	restartAnimationSystem(character)
	-- 等待 Humanoid 加载（超时时间设为2秒）
	local humanoid = character:WaitForChild("Humanoid", 2)
	if not humanoid then
		warn("Humanoid 加载超时，无法更新相机")
		return
	end

	-- 更新相机跟随
	local camera = workspace.CurrentCamera
	camera.CameraSubject = humanoid

	-- 设置相机初始位置
	task.wait() -- 等待一帧确保角色完全加载
	local cameraOffset = character:FindFirstChild("CameraOffset")
	if cameraOffset and cameraOffset:IsA("Vector3Value") then
		camera.CFrame = camera.CFrame * CFrame.new(cameraOffset.Value)
	end
end

-- 初始化客户端
function ProfessionSwitch.InitClient()

	initProfessionTriggers()
	NotifyManager.RegisterClientEvent("HasProfession",function(data)
		HasProfession = data.hasProfession
		refreshAllTriggers() -- 刷新所有触发器显示
	end)
	NotifyManager.RegisterClientEvent("RecordProfession",function(data)
		local profession = data.profession
		table.insert(HasProfession, profession)
		refreshTriggers(profession)
	end)
	-- 获取本地玩家
	local localPlayer = Players.LocalPlayer

	-- 监听本地玩家的角色变更（包括后续切换）
	if localPlayer.Character then
		handleCharacterChange(localPlayer.Character)
	end

	localPlayer.CharacterAdded:Connect(handleCharacterChange)
	
	
end

return ProfessionSwitch    
