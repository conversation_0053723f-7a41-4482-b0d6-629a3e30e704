local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local WeaponServer = require(ReplicatedStorage.Scripts.Server.Services.WeaponServer)
local ProtocolManager = require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local WeaponManager = {}

-- 禁用工作区中所有武器的触摸交互
function WeaponManager:DisableWeaponInteractions(player)
	if not player or not player.Character then return end

	-- 获取角色位置
	local character = player.Character
	local rootPart = character:FindFirstChild("HumanoidRootPart")
	if not rootPart then return end

	-- 创建一个触发区域，用于检测附近的武器
	local detectionRadius = 50 -- 检测半径

	-- 使用OverlapParams查找附近的部件
	local overlapParams = OverlapParams.new()
	overlapParams.FilterType = Enum.RaycastFilterType.Include
	overlapParams.FilterDescendantsInstances = {workspace} -- 只检查工作区

	-- 获取球形区域内的所有部件
	local partsInRadius = workspace:GetPartBoundsInRadius(rootPart.Position, detectionRadius, overlapParams)

	-- 遍历找到的部件，寻找武器
	for _, part in pairs(partsInRadius) do
		-- 检查是否是工具或工具的一部分
		local tool = part:FindFirstAncestorOfClass("Tool")
		if tool and tool.Parent == workspace then
			-- 临时禁用武器的CanTouch属性
			local handle = tool:FindFirstChild("Handle")
			if handle then
				-- 保存原始状态
				if not tool:GetAttribute("OriginalCanTouch") then
					tool:SetAttribute("OriginalCanTouch", handle.CanTouch)
				end

				-- 禁用触摸交互
				handle.CanTouch = false

				-- 添加到禁用列表
				self.DisabledWeapons = self.DisabledWeapons or {}
				self.DisabledWeapons[tool] = player.UserId

				print("已禁用武器的触摸交互: " .. tool.Name)
			end
		end
	end
end

-- 恢复武器的触摸交互
function WeaponManager:RestoreWeaponInteractions(player)
	if not self.DisabledWeapons then return end

	-- 恢复被该玩家禁用的武器
	for tool, userId in pairs(self.DisabledWeapons) do
		if userId == player.UserId and tool:IsA("Tool") and tool.Parent == workspace then
			local handle = tool:FindFirstChild("Handle")
			if handle then
				-- 恢复原始状态
				local originalCanTouch = tool:GetAttribute("OriginalCanTouch")
				if originalCanTouch ~= nil then
					handle.CanTouch = originalCanTouch
					tool:SetAttribute("OriginalCanTouch", nil)
				else
					handle.CanTouch = true
				end

				-- 从禁用列表中移除
				self.DisabledWeapons[tool] = nil

				print("已恢复武器的触摸交互: " .. tool.Name)
			end
		end
	end
end

-- 监听玩家工具装备和卸下
local function setupPlayerToolEvents(player)
	local function onCharacterAdded(character)
		-- 监听工具装备
		character.ChildAdded:Connect(function(child)
			if child:IsA("Tool") then
				WeaponServer:UpdatePlayerWeapon(player, child.Name)
			end
		end)

		-- 监听工具卸下
		character.ChildRemoved:Connect(function(child)
			if child:IsA("Tool") then
				WeaponServer:ClearPlayerWeapon(player)
			end
		end)
	end

	-- 当前角色
	if player.Character then
		onCharacterAdded(player.Character)
	end

	-- 监听角色重生
	player.CharacterAdded:Connect(onCharacterAdded)
end

function WeaponManager.Init()
	-- 处理已在服务器的玩家
	for _, player in ipairs(Players:GetPlayers()) do
		setupPlayerToolEvents(player)
	end

	-- 监听新玩家加入
	Players.PlayerAdded:Connect(setupPlayerToolEvents)

	-- 注意：武器相关事件通过ProtocolManager中注册的事件处理函数处理
	-- 不需要在这里重复注册事件
end

return WeaponManager