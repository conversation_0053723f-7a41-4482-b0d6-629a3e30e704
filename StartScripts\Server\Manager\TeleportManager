local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TeleportService = game:GetService("TeleportService")
local ProfessionManager = require(ReplicatedStorage.Scripts.Server.Manager.ProfessionManager)
local TARGET_PLACE_ID = 89908111666289
local TeleportManager = {}

-- 安全获取玩家函数
local function getFirstPlayer()
	local players = Players:GetPlayers()
	if #players > 0 then
		return players[1]
	else
		warn("当前没有玩家在线")
		return nil
	end
end

-- 主函数
function TeleportManager:teleportPlayers(players)
	if #players == 0 then
		warn("没有玩家可传送")
		return
	end
	local teleportOptions = Instance.new("TeleportOptions")
	teleportOptions:SetTeleportData({
		
		sourcePlace = game.PlaceId,
		timestamp = os.time(),
		items = {
			
			{ id = 10039, quantity = 2, itemType = 11 },  -- 消耗品，放背包
			{ id = 10052, quantity = 1, itemType = 10  } , -- 武器，放工具栏
			{ id = 10029, quantity = 50, itemType =6},
			{ id = 10030, quantity = 50, itemType = 6  } ,
			{ id = 10051, quantity = 1, itemType = 10  } 
		},
		
		professionPlayer = ProfessionManager.playerProfession,
		professionState = ProfessionManager.playerProfessionState
	})
	local success, err = pcall(function()
		-- 第二个参数就是玩家数组，符合 TeleportAsync 要求
		TeleportService:TeleportAsync(TARGET_PLACE_ID, players, teleportOptions)
	end)
	
	print("传输数据成功")
	if not success then
		warn("传送失败:", err)
	end
end

return TeleportManager