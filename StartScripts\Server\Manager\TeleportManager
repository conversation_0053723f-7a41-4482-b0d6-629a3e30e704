local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TeleportService = game:GetService("TeleportService")
local ProfessionManager = require(ReplicatedStorage.Scripts.Server.Manager.ProfessionManager)
local TARGET_PLACE_ID = 89908111666289
local TeleportManager = {}

-- 安全获取玩家函数
local function getFirstPlayer()
	local players = Players:GetPlayers()
	if #players > 0 then
		return players[1]
	else
		warn("当前没有玩家在线")
		return nil
	end
end

-- 为每个玩家生成个性化数据
local function generatePlayersData(players)
	local playersData = {}

	for _, player in ipairs(players) do
		local userId = player.UserId
		local playerItems = {}
		local profession = nil
		local professionId = nil

		-- 从 ProfessionManager 获取玩家的物品数据
		if ProfessionManager.playerItems and ProfessionManager.playerItems[userId] then
			local playerData = ProfessionManager.playerItems[userId]
			playerItems = playerData.items
			profession = playerData.profession
			professionId = playerData.professionId
			print("TeleportManager: 玩家 " .. player.Name .. " 有 " .. #playerItems .. " 个物品")
		else
			warn("TeleportManager: 玩家 " .. player.Name .. " 没有职业物品数据")
		end

		playersData[player.Name] = {
			items = playerItems,
			profession = profession,
			professionId = professionId,
			userId = userId
		}
	end

	return playersData
end

-- 主函数
function TeleportManager:teleportPlayers(players)
	if #players == 0 then
		warn("没有玩家可传送")
		return
	end

	-- 生成每个玩家的个性化数据
	local playersData = generatePlayersData(players)

	local teleportOptions = Instance.new("TeleportOptions")
	teleportOptions:SetTeleportData({
		sourcePlace = game.PlaceId,
		timestamp = os.time(),
		-- 传送个性化的玩家数据，以玩家名字为键
		playersData = playersData,
		-- 保留原有的全局职业数据（向后兼容）
		professionPlayer = ProfessionManager.playerProfession,
		professionState = ProfessionManager.playerProfessionState
	})

	local success, err = pcall(function()
		TeleportService:TeleportAsync(TARGET_PLACE_ID, players, teleportOptions)
	end)

	if success then
		print("传输数据成功，已为 " .. #players .. " 个玩家生成个性化数据")
		-- 调试输出传送的数据
		for playerName, data in pairs(playersData) do
			print("玩家 " .. playerName .. " 职业:" .. (data.profession or "无") .. " 物品数量:" .. #data.items)
		end
	else
		warn("传送失败:", err)
	end
end

return TeleportManager