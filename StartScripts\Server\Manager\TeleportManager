local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TeleportService = game:GetService("TeleportService")
local ProfessionManager = require(ReplicatedStorage.Scripts.Server.Manager.ProfessionManager)
local OccupationConfig = require(ReplicatedStorage.Scripts.Config.OccupationConfig)
local OccupationDataParser = require(ReplicatedStorage.Scripts.Server.Utils.OccupationDataParser)
local TARGET_PLACE_ID = 89908111666289
local TeleportManager = {}

-- 安全获取玩家函数
local function getFirstPlayer()
	local players = Players:GetPlayers()
	if #players > 0 then
		return players[1]
	else
		warn("当前没有玩家在线")
		return nil
	end
end

-- 为单个玩家生成个性化物品数据
-- @param player: Player - 玩家对象
-- @return table - 该玩家的物品数据数组
local function generatePlayerItems(player)
	local userId = player.UserId
	local playerProfessionId = ProfessionManager.playerProfessionState[userId]

	if not playerProfessionId then
		warn("TeleportManager: 玩家 " .. player.Name .. " 没有职业数据，使用默认物品")
		return {}
	end

	-- 从 OccupationConfig 获取该职业的物品数据
	local items = OccupationDataParser.getItemsByOccupationId(playerProfessionId, OccupationConfig)

	-- 调试输出
	OccupationDataParser.debugPrintItems(items, player.Name)

	return items
end

-- 为所有玩家生成个性化数据
-- @param players: table - 玩家数组
-- @return table - 包含每个玩家个性化数据的表
local function generatePlayersData(players)
	local playersData = {}

	for _, player in ipairs(players) do
		local userId = player.UserId
		playersData[tostring(userId)] = {
			items = generatePlayerItems(player),
			profession = ProfessionManager.playerProfession[userId],
			professionId = ProfessionManager.playerProfessionState[userId],
			playerName = player.Name
		}
	end

	return playersData
end

-- 主函数
function TeleportManager:teleportPlayers(players)
	if #players == 0 then
		warn("没有玩家可传送")
		return
	end

	-- 生成每个玩家的个性化数据
	local playersData = generatePlayersData(players)

	local teleportOptions = Instance.new("TeleportOptions")
	teleportOptions:SetTeleportData({
		sourcePlace = game.PlaceId,
		timestamp = os.time(),
		-- 传送个性化的玩家数据而不是固定的物品列表
		playersData = playersData,
		-- 保留原有的全局职业数据（向后兼容）
		professionPlayer = ProfessionManager.playerProfession,
		professionState = ProfessionManager.playerProfessionState
	})

	local success, err = pcall(function()
		TeleportService:TeleportAsync(TARGET_PLACE_ID, players, teleportOptions)
	end)

	if success then
		print("传输数据成功，已为 " .. #players .. " 个玩家生成个性化数据")
		-- 调试输出传送的数据
		for userId, data in pairs(playersData) do
			print("玩家 " .. data.playerName .. " (ID:" .. userId .. ") 职业:" .. (data.profession or "无") .. " 物品数量:" .. #data.items)
		end
	else
		warn("传送失败:", err)
	end
end

return TeleportManager